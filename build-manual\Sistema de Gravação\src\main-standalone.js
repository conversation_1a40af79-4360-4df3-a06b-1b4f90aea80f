const { app, BrowserWindow, ipcMain, desktopCapturer, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const { checkPermissions } = require('./permissions');

// Configurar FFmpeg para usar o executável embutido
function findFFmpegPath() {
  const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
  const isPackaged = app.isPackaged;
  
  console.log(`🔧 Modo: ${isDev ? 'Desenvolvimento' : 'Produção'}, Empacotado: ${isPackaged}`);
  
  // Lista de caminhos possíveis para o FFmpeg
  const possiblePaths = [];
  
  if (isPackaged) {
    // Quando empacotado, o FFmpeg está em resources
    const resourcesPath = process.resourcesPath;
    possiblePaths.push(
      path.join(resourcesPath, 'ffmpeg.exe'),
      path.join(resourcesPath, 'ffmpeg-static.exe')
    );
  } else {
    // Em desenvolvimento, usar os módulos node
    try {
      possiblePaths.push(require('@ffmpeg-installer/ffmpeg').path);
    } catch (e) { /* ignorar */ }
    
    try {
      possiblePaths.push(require('ffmpeg-static'));
    } catch (e) { /* ignorar */ }
  }
  
  // Adicionar caminhos de fallback
  possiblePaths.push(
    path.join(__dirname, '..', 'resources', 'ffmpeg.exe'),
    path.join(__dirname, '..', 'ffmpeg.exe'),
    path.join(process.cwd(), 'ffmpeg.exe'),
    'ffmpeg' // Tentar FFmpeg do sistema como último recurso
  );
  
  // Testar cada caminho
  for (const testPath of possiblePaths) {
    if (testPath && fs.existsSync(testPath)) {
      console.log(`✅ FFmpeg encontrado em: ${testPath}`);
      return testPath;
    }
  }
  
  console.error('❌ FFmpeg não encontrado em nenhum dos caminhos testados:');
  possiblePaths.forEach(p => console.log(`   - ${p}`));
  
  // Retornar o primeiro caminho como fallback
  return possiblePaths[0] || 'ffmpeg';
}

let ffmpegPath = findFFmpegPath();
console.log('🔧 Configurando FFmpeg com caminho:', ffmpegPath);

let mainWindow;
let areaSelectorWindow;
let isRecording = false;
let selectedArea = null;

// Testar FFmpeg na inicialização (versão simplificada)
function testFFmpeg() {
  return new Promise((resolve, reject) => {
    const ffmpegProcess = spawn(ffmpegPath, ['-version'], { stdio: 'pipe' });
    
    ffmpegProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ FFmpeg está funcionando corretamente!');
        resolve(true);
      } else {
        console.error('❌ FFmpeg não está disponível');
        reject(new Error('FFmpeg test failed'));
      }
    });
    
    ffmpegProcess.on('error', (err) => {
      console.error('❌ Erro ao testar FFmpeg:', err.message);
      reject(err);
    });
  });
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    titleBarStyle: 'hiddenInset',
    frame: false,
    backgroundColor: '#1a1a1a',
    show: false
  });

  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Capturar erros não tratados
  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('❌ Renderer process crashed:', { killed });
  });

  mainWindow.webContents.on('unresponsive', () => {
    console.error('❌ Renderer process became unresponsive');
  });

  // Atalho para DevTools
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.control && input.shift && input.key.toLowerCase() === 'i') {
      mainWindow.webContents.openDevTools();
    }
  });
}

app.whenReady().then(async () => {
  // Configurar flags para melhor captura de tela
  app.commandLine.appendSwitch('enable-usermedia-screen-capturing');
  app.commandLine.appendSwitch('allow-http-screen-capture');
  app.commandLine.appendSwitch('auto-select-desktop-capture-source', 'Screen Recorder Pro');
  
  try {
    // Verificar permissões
    const permissions = await checkPermissions();
    console.log('🔐 Permissões verificadas:', permissions);
    
    await testFFmpeg();
    createWindow();
  } catch (error) {
    console.error('❌ Erro ao inicializar:', error.message);
    createWindow();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers básicos (sem compressão avançada para evitar dependências)
ipcMain.handle('get-sources', async () => {
  try {
    console.log('🔍 Buscando fontes de captura...');
    
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 300, height: 200 },
      fetchWindowIcons: true
    });
    
    console.log(`✅ Encontradas ${sources.length} fontes`);
    return sources;
  } catch (error) {
    console.error('❌ Erro ao obter fontes:', error);
    return [];
  }
});

ipcMain.handle('save-video', async (event, buffer, filename) => {
  try {
    const { filePath } = await dialog.showSaveDialog(mainWindow, {
      defaultPath: filename,
      filters: [
        { name: 'Vídeos', extensions: ['mp4', 'webm', 'avi'] }
      ]
    });

    if (filePath) {
      fs.writeFileSync(filePath, buffer);
      return filePath;
    }
    return null;
  } catch (error) {
    console.error('Erro ao salvar vídeo:', error);
    return null;
  }
});

// Compressão simplificada usando FFmpeg direto
ipcMain.handle('simple-compress-video', async (event, inputPath, outputPath) => {
  return new Promise((resolve, reject) => {
    console.log(`🎬 Iniciando compressão: ${inputPath} -> ${outputPath}`);
    
    const args = [
      '-i', inputPath,
      '-c:v', 'libx264',
      '-preset', 'fast',
      '-crf', '28',
      '-an', // Sem áudio para simplicidade
      '-y', // Sobrescrever arquivo de saída
      outputPath
    ];
    
    const ffmpegProcess = spawn(ffmpegPath, args);
    
    ffmpegProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Compressão concluída!');
        resolve(outputPath);
      } else {
        console.error('❌ Erro na compressão');
        reject(new Error('Compression failed'));
      }
    });
    
    ffmpegProcess.on('error', (err) => {
      console.error('❌ Erro ao executar FFmpeg:', err.message);
      reject(err);
    });
  });
});

ipcMain.handle('open-file', async (event, filePath) => {
  shell.openPath(filePath);
});

ipcMain.handle('minimize-window', () => {
  mainWindow.minimize();
});

ipcMain.handle('close-window', () => {
  mainWindow.close();
});

ipcMain.handle('test-ffmpeg', async () => {
  try {
    await testFFmpeg();
    return { success: true, message: 'FFmpeg está funcionando corretamente!' };
  } catch (error) {
    return { success: false, message: `Erro no FFmpeg: ${error.message}` };
  }
});

ipcMain.handle('check-permissions', async () => {
  try {
    const permissions = await checkPermissions();
    return { success: true, permissions };
  } catch (error) {
    return { success: false, message: error.message };
  }
});

ipcMain.handle('check-file-exists', async (event, filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error('Erro ao verificar arquivo:', error);
    return false;
  }
});
