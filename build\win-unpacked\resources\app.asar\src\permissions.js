// Configurações de permissões para captura de tela
const { systemPreferences } = require('electron');

async function requestScreenCapturePermission() {
  if (process.platform === 'darwin') {
    // macOS - solicitar permissão de captura de tela
    const hasPermission = systemPreferences.getMediaAccessStatus('screen');
    
    if (hasPermission !== 'granted') {
      console.log('🔐 Solicitando permissão de captura de tela...');
      await systemPreferences.askForMediaAccess('screen');
    }
    
    return systemPreferences.getMediaAccessStatus('screen') === 'granted';
  }
  
  // Windows/Linux - geralmente não precisam de permissão explícita
  return true;
}

async function checkPermissions() {
  try {
    const screenPermission = await requestScreenCapturePermission();
    
    console.log('📋 Status das permissões:');
    console.log(`  Captura de tela: ${screenPermission ? '✅' : '❌'}`);
    
    return {
      screen: screenPermission
    };
  } catch (error) {
    console.error('❌ Erro ao verificar permissões:', error);
    return {
      screen: false
    };
  }
}

module.exports = {
  requestScreenCapturePermission,
  checkPermissions
};