<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Global</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Global</h1>

    



<section>

<header>
    <h2>
    
    </h2>
    
</header>

<article>
    <div class="container-overview">
    

    
        

        
<dl class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<dt>
    <h4 class="name" id="createSizeFilters"><span class="type-signature">&lt;private> </span>createSizeFilters<span class="signature">(command, key, value)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Recompute size filters</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html">FfmpegCommand</a></span>


            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>newly-added parameter name ('size', 'aspect' or 'pad')</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>newly-added parameter value</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="videosize.js.html">options/videosize.js</a>, <a href="videosize.js.html#line72">line 72</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>filter string array</p>
</div>



        

    
</dd>

        
            
<dt>
    <h4 class="name" id="getScalePadFilters"><span class="type-signature">&lt;private> </span>getScalePadFilters<span class="signature">(width, height, aspect, color)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Return filters to pad video to width*height,</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>width</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>output width</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>height</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>output height</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>aspect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>video aspect ratio (without padding)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>color</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>padding color</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="videosize.js.html">options/videosize.js</a>, <a href="videosize.js.html#line19">line 19</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>scale/pad filters</p>
</div>



        

    
</dd>

        
            
<dt>
    <h4 class="name" id="parseProgressLine"><span class="type-signature">&lt;private> </span>parseProgressLine<span class="signature">(line)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Parse progress line from ffmpeg stderr</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>line</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p>progress line</p></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="utils.js.html">utils.js</a>, <a href="utils.js.html#line16">line 16</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>progress object</p>
</div>



        

    
</dd>

        
            
<dt>
    <h4 class="name" id="process"><span class="type-signature">&lt;private> </span>process<span class="signature">(command, target, <span class="optional">pipeOptions</span>)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html">FfmpegCommand</a></span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Writable</span>


            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>pipeOptions</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line47">line 47</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<dt>
    <h4 class="name" id="runFfprobe"><span class="type-signature">&lt;private> </span>runFfprobe<span class="signature">(command)</span><span class="type-signature"></span></h4>

    
</dt>
<dd>

    
    <div class="description">
        <p>Run ffprobe asynchronously and store data in command</p>
    </div>
    

    

    

    
        <h5>Parameters:</h5>
        

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="FfmpegCommand.html">FfmpegCommand</a></span>


            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>
    

    
<dl class="details">
    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="processor.js.html">processor.js</a>, <a href="processor.js.html#line194">line 194</a>
    </li></ul></dd>
    

    

    

    
</dl>


    

    

    

    

    

    

    
</dd>

        </dl>
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Index</a></h2><h3>Classes</h3><ul><li><a href="FfmpegCommand.html">FfmpegCommand</a></li></ul><h3>Events</h3><ul><li><a href="FfmpegCommand.html#event:codecData">codecData</a></li><li><a href="FfmpegCommand.html#event:end">end</a></li><li><a href="FfmpegCommand.html#event:error">error</a></li><li><a href="FfmpegCommand.html#event:progress">progress</a></li><li><a href="FfmpegCommand.html#event:start">start</a></li></ul><h3>Global</h3><ul><li><a href="global.html#createSizeFilters">createSizeFilters</a></li><li><a href="global.html#getScalePadFilters">getScalePadFilters</a></li><li><a href="global.html#parseProgressLine">parseProgressLine</a></li><li><a href="global.html#process">process</a></li><li><a href="global.html#runFfprobe">runFfprobe</a></li></ul>
</nav>

<br clear="both">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.3.0-alpha5</a> on Thu May 01 2014 13:29:29 GMT+0200 (CEST)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>