const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getSources: () => ipcRenderer.invoke('get-sources'),
  saveVideo: (buffer, filename) => ipcRenderer.invoke('save-video', buffer, filename),
  compressVideo: (inputPath, outputPath, options) => ipcRenderer.invoke('compress-video', inputPath, outputPath, options),
  openFile: (filePath) => ipcRenderer.invoke('open-file', filePath),
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  testFFmpeg: () => ipcRenderer.invoke('test-ffmpeg'),
  checkPermissions: () => ipcRenderer.invoke('check-permissions'),
  openAreaSelector: () => ipcRenderer.invoke('open-area-selector'),
  getSelectedArea: () => ipcRenderer.invoke('get-selected-area'),
  simpleCompressVideo: (inputPath, outputPath) => ipcRenderer.invoke('simple-compress-video', inputPath, outputPath),
  checkFileExists: (filePath) => ipcRenderer.invoke('check-file-exists', filePath),
  
  // Listeners para eventos
  onCompressionProgress: (callback) => {
    ipcRenderer.on('compression-progress', (event, progress) => callback(progress));
  },
  
  onAreaSelectionComplete: (callback) => {
    ipcRenderer.on('area-selection-complete', (event, area) => callback(area));
  },
  
  onAreaSelectionCancelled: (callback) => {
    ipcRenderer.on('area-selection-cancelled', () => callback());
  },
  
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});