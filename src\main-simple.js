const { app, BrowserWindow, ipcMain, desktopCapturer, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// Importar fluent-ffmpeg apenas se disponível
let ffmpeg;
try {
  ffmpeg = require('fluent-ffmpeg');
} catch (error) {
  console.log('⚠️ fluent-ffmpeg não disponível');
  ffmpeg = null;
}

const { checkPermissions } = require('./permissions');

let ffmpegPath;
let mainWindow;
let areaSelectorWindow;
let isRecording = false;
let selectedArea = null;

// Configurar FFmpeg para usar o executável embutido
function findFFmpegPath() {
  console.log('🔧 Procurando FFmpeg...');
  
  // Lista de caminhos possíveis para o FFmpeg
  const possiblePaths = [];
  
  // Tentar usar os módulos node primeiro
  try {
    const installerPath = require('@ffmpeg-installer/ffmpeg').path;
    possiblePaths.push(installerPath);
    console.log('📦 Encontrado @ffmpeg-installer:', installerPath);
  } catch (e) {
    console.log('⚠️ @ffmpeg-installer não encontrado');
  }
  
  try {
    const staticPath = require('ffmpeg-static');
    possiblePaths.push(staticPath);
    console.log('📦 Encontrado ffmpeg-static:', staticPath);
  } catch (e) {
    console.log('⚠️ ffmpeg-static não encontrado');
  }
  
  // Adicionar caminhos de fallback
  possiblePaths.push(
    path.join(__dirname, '..', 'resources', 'ffmpeg.exe'),
    path.join(__dirname, '..', 'ffmpeg.exe'),
    path.join(process.cwd(), 'ffmpeg.exe'),
    'ffmpeg'
  );
  
  // Testar cada caminho
  for (const testPath of possiblePaths) {
    if (testPath && fs.existsSync(testPath)) {
      console.log(`✅ FFmpeg encontrado em: ${testPath}`);
      return testPath;
    }
  }
  
  console.error('❌ FFmpeg não encontrado em nenhum dos caminhos testados');
  return possiblePaths[0] || 'ffmpeg';
}

// Função para inicializar FFmpeg
function initializeFFmpeg() {
  ffmpegPath = findFFmpegPath();
  console.log('🔧 Configurando FFmpeg com caminho:', ffmpegPath);

  // Configurar FFmpeg apenas se disponível
  if (ffmpeg && ffmpegPath) {
    ffmpeg.setFfmpegPath(ffmpegPath);
  }
}

// Testar FFmpeg na inicialização
function testFFmpeg() {
  return new Promise((resolve, reject) => {
    if (!ffmpeg) {
      // Se fluent-ffmpeg não está disponível, testar FFmpeg diretamente
      const { spawn } = require('child_process');
      const ffmpegProcess = spawn(ffmpegPath, ['-version'], { stdio: 'pipe' });
      
      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ FFmpeg está funcionando corretamente!');
          resolve(true);
        } else {
          console.error('❌ FFmpeg não está disponível');
          reject(new Error('FFmpeg test failed'));
        }
      });
      
      ffmpegProcess.on('error', (err) => {
        console.error('❌ Erro ao testar FFmpeg:', err.message);
        reject(err);
      });
      
      return;
    }
    
    // Usar fluent-ffmpeg se disponível
    ffmpeg.getAvailableFormats((err, formats) => {
      if (err) {
        console.error('❌ FFmpeg não está disponível:', err.message);
        reject(err);
        return;
      }
      console.log('✅ FFmpeg está funcionando corretamente!');
      resolve(true);
    });
  });
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    titleBarStyle: 'hiddenInset',
    frame: false,
    backgroundColor: '#1a1a1a',
    show: false
  });

  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Atalho para DevTools
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.control && input.shift && input.key.toLowerCase() === 'i') {
      mainWindow.webContents.openDevTools();
    }
  });
}

app.whenReady().then(async () => {
  // Configurar flags para melhor captura de tela
  app.commandLine.appendSwitch('enable-usermedia-screen-capturing');
  app.commandLine.appendSwitch('allow-http-screen-capture');
  app.commandLine.appendSwitch('auto-select-desktop-capture-source', 'Screen Recorder Pro');
  
  try {
    // Inicializar FFmpeg agora que o app está pronto
    initializeFFmpeg();
    
    // Verificar permissões
    const permissions = await checkPermissions();
    console.log('🔐 Permissões verificadas:', permissions);
    
    await testFFmpeg();
    createWindow();
  } catch (error) {
    console.error('❌ Erro ao inicializar:', error.message);
    createWindow();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers básicos
ipcMain.handle('get-sources', async () => {
  try {
    console.log('🔍 Buscando fontes de captura...');
    
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 300, height: 200 },
      fetchWindowIcons: true
    });
    
    console.log(`✅ Encontradas ${sources.length} fontes`);
    return sources;
  } catch (error) {
    console.error('❌ Erro ao obter fontes:', error);
    return [];
  }
});

ipcMain.handle('save-video', async (event, buffer, filename) => {
  try {
    const { filePath } = await dialog.showSaveDialog(mainWindow, {
      defaultPath: filename,
      filters: [
        { name: 'Vídeos', extensions: ['mp4', 'webm', 'avi'] }
      ]
    });

    if (filePath) {
      fs.writeFileSync(filePath, buffer);
      return filePath;
    }
    return null;
  } catch (error) {
    console.error('Erro ao salvar vídeo:', error);
    return null;
  }
});

ipcMain.handle('open-file', async (event, filePath) => {
  shell.openPath(filePath);
});

ipcMain.handle('minimize-window', () => {
  mainWindow.minimize();
});

ipcMain.handle('close-window', () => {
  mainWindow.close();
});

ipcMain.handle('test-ffmpeg', async () => {
  try {
    await testFFmpeg();
    return { success: true, message: 'FFmpeg está funcionando corretamente!' };
  } catch (error) {
    return { success: false, message: `Erro no FFmpeg: ${error.message}` };
  }
});

ipcMain.handle('check-permissions', async () => {
  try {
    const permissions = await checkPermissions();
    return { success: true, permissions };
  } catch (error) {
    return { success: false, message: error.message };
  }
});

ipcMain.handle('check-file-exists', async (event, filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error('Erro ao verificar arquivo:', error);
    return false;
  }
});

// Compressão simplificada (se fluent-ffmpeg estiver disponível)
if (ffmpeg) {
  ipcMain.handle('simple-compress-video', async (event, inputPath, outputPath) => {
    return new Promise((resolve, reject) => {
      console.log(`🎬 Iniciando compressão: ${inputPath} -> ${outputPath}`);
      
      ffmpeg(inputPath)
        .videoCodec('libx264')
        .videoBitrate('1000k')
        .noAudio()
        .addOption('-preset', 'fast')
        .addOption('-crf', '28')
        .addOption('-movflags', '+faststart')
        .output(outputPath)
        .on('end', () => {
          console.log('✅ Compressão concluída!');
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('❌ Erro na compressão:', err.message);
          reject(new Error(`Erro na compressão: ${err.message}`));
        })
        .run();
    });
  });
}
