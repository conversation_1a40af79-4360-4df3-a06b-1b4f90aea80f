// @flow
// Generated using flowgen2

import type {CachedResponse} from './CachedResponse';

declare class MemoryCache {
  getResponse(
    url: string,
    callback: (err: null | Error, response: null | CachedResponse) => void,
  ): void;
  updateResponseHeaders(
    url: string,
    response: {[key: 'headers' | 'requestTimestamp']: any},
  ): void;
  setResponse(url: string, response: CachedResponse): void;
  invalidateResponse(
    url: string,
    callback: (err: ErrnoError | null) => void,
  ): void;
}
export default MemoryCache;
