const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Criando build standalone (sem dependência do Node.js)...\n');

// Função para copiar arquivos recursivamente
function copyRecursive(src, dest) {
  const stats = fs.statSync(src);
  
  if (stats.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    for (const file of files) {
      copyRecursive(path.join(src, file), path.join(dest, file));
    }
  } else {
    const destDir = path.dirname(dest);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    fs.copyFileSync(src, dest);
  }
}

// Função para limpar diretório
function cleanDir(dir) {
  if (fs.existsSync(dir)) {
    try {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ Limpou diretório: ${dir}`);
    } catch (error) {
      console.log(`⚠️ Não foi possível limpar ${dir}: ${error.message}`);
    }
  }
}

try {
  // 1. Verificar se o Electron está disponível
  console.log('🔍 Verificando Electron...');
  const electronPath = path.join('./node_modules', '.bin', 'electron.cmd');
  if (!fs.existsSync(electronPath)) {
    console.log('❌ Electron não encontrado. Execute: npm install');
    process.exit(1);
  }
  
  // 2. Limpar diretório de build
  console.log('🧹 Limpando diretório de build...');
  const buildDir = './build-standalone';
  cleanDir(buildDir);
  
  // 3. Criar estrutura básica
  console.log('📁 Criando estrutura...');
  const appDir = path.join(buildDir, 'Sistema de Gravação Standalone');
  const resourcesDir = path.join(appDir, 'resources');
  
  fs.mkdirSync(appDir, { recursive: true });
  fs.mkdirSync(resourcesDir, { recursive: true });
  
  // 4. Copiar binários do Electron
  console.log('⚡ Copiando Electron...');
  const electronDir = './node_modules/electron/dist';
  if (fs.existsSync(electronDir)) {
    copyRecursive(electronDir, appDir);
    
    // Renomear electron.exe para o nome da aplicação
    const electronExe = path.join(appDir, 'electron.exe');
    const appExe = path.join(appDir, 'Sistema de Gravação.exe');
    
    if (fs.existsSync(electronExe)) {
      fs.renameSync(electronExe, appExe);
      console.log('✅ Electron copiado e renomeado');
    }
  } else {
    console.log('❌ Diretório do Electron não encontrado');
    process.exit(1);
  }
  
  // 5. Criar app.asar com o código
  console.log('📦 Criando pacote da aplicação...');
  const tempAppDir = path.join(buildDir, 'temp-app');
  fs.mkdirSync(tempAppDir, { recursive: true });
  
  // Copiar código fonte
  copyRecursive('./src', path.join(tempAppDir, 'src'));
  
  // Criar package.json simplificado
  const packageJson = {
    name: 'sistema-de-gravacao',
    version: '1.0.0',
    main: 'src/main.js',
    dependencies: {}
  };
  fs.writeFileSync(path.join(tempAppDir, 'package.json'), JSON.stringify(packageJson, null, 2));
  
  // Copiar dependências essenciais
  const nodeModulesTemp = path.join(tempAppDir, 'node_modules');
  fs.mkdirSync(nodeModulesTemp, { recursive: true });
  
  const essentialModules = ['fluent-ffmpeg'];
  for (const module of essentialModules) {
    const srcPath = path.join('./node_modules', module);
    const destPath = path.join(nodeModulesTemp, module);
    
    if (fs.existsSync(srcPath)) {
      copyRecursive(srcPath, destPath);
      console.log(`  📦 Copiado: ${module}`);
    }
  }
  
  // 6. Copiar FFmpeg para resources
  console.log('🎬 Copiando FFmpeg...');
  const ffmpegPaths = [
    {
      src: './node_modules/@ffmpeg-installer/win32-x64/ffmpeg.exe',
      dest: path.join(resourcesDir, 'ffmpeg.exe')
    },
    {
      src: './node_modules/ffmpeg-static/ffmpeg.exe',
      dest: path.join(resourcesDir, 'ffmpeg-static.exe')
    }
  ];
  
  for (const { src, dest } of ffmpegPaths) {
    if (fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`  ✅ Copiado: ${path.basename(dest)}`);
    } else {
      console.log(`  ❌ Não encontrado: ${src}`);
    }
  }
  
  // 7. Criar app.asar
  console.log('📦 Criando app.asar...');
  try {
    const asarPath = path.join(appDir, 'resources', 'app.asar');
    execSync(`npx asar pack "${tempAppDir}" "${asarPath}"`, { stdio: 'inherit' });
    console.log('✅ app.asar criado');
  } catch (error) {
    console.log('⚠️ Erro ao criar app.asar, copiando arquivos diretamente...');
    const appResourcesDir = path.join(appDir, 'resources', 'app');
    copyRecursive(tempAppDir, appResourcesDir);
  }
  
  // 8. Limpar arquivos temporários
  cleanDir(tempAppDir);
  
  // 9. Criar scripts auxiliares
  console.log('📄 Criando scripts auxiliares...');
  
  const startBat = `@echo off
echo Iniciando Sistema de Gravação...
cd /d "%~dp0"
"Sistema de Gravação.exe"`;
  
  fs.writeFileSync(path.join(appDir, 'Iniciar.bat'), startBat);
  
  const readme = `# Sistema de Gravação - Versão Standalone

## Como usar:
1. Execute "Sistema de Gravação.exe" OU "Iniciar.bat"
2. Não é necessário instalar Node.js ou outras dependências

## Características:
- ✅ Executável standalone (não precisa de instalação)
- ✅ FFmpeg incluído
- ✅ Funciona em qualquer PC Windows
- ✅ Não requer privilégios de administrador

## Arquivos:
- Sistema de Gravação.exe - Aplicativo principal
- resources/ - Recursos da aplicação (incluindo FFmpeg)
- Iniciar.bat - Script alternativo para iniciar

## Distribuição:
Comprima toda a pasta em um ZIP e distribua.
O usuário só precisa extrair e executar.

Versão: 1.0.0
`;
  
  fs.writeFileSync(path.join(appDir, 'README.txt'), readme);
  
  // 10. Verificar build
  console.log('\n🔍 Verificando build standalone...');
  const exeExists = fs.existsSync(path.join(appDir, 'Sistema de Gravação.exe'));
  const ffmpegExists = fs.existsSync(path.join(resourcesDir, 'ffmpeg.exe'));
  const appAsarExists = fs.existsSync(path.join(appDir, 'resources', 'app.asar')) || 
                        fs.existsSync(path.join(appDir, 'resources', 'app', 'src', 'main.js'));
  
  console.log(`  Executável: ${exeExists ? '✅' : '❌'}`);
  console.log(`  FFmpeg: ${ffmpegExists ? '✅' : '❌'}`);
  console.log(`  Aplicação: ${appAsarExists ? '✅' : '❌'}`);
  
  if (exeExists && ffmpegExists && appAsarExists) {
    console.log('\n🎉 Build standalone concluído com sucesso!');
    console.log(`📁 Localização: ${path.resolve(appDir)}`);
    console.log('\n💡 Para testar:');
    console.log('1. Navegue até a pasta do build');
    console.log('2. Execute "Sistema de Gravação.exe"');
    console.log('\n📦 Para distribuir:');
    console.log('1. Comprima a pasta inteira em um ZIP');
    console.log('2. O usuário só precisa extrair e executar');
    console.log('3. Não requer instalação de Node.js ou outras dependências');
    
    // Calcular tamanho
    const stats = fs.statSync(appDir);
    console.log(`\n📊 Tamanho aproximado: ~150-200MB (incluindo Electron + FFmpeg)`);
  } else {
    console.log('\n❌ Build incompleto. Verifique os erros acima.');
  }
  
} catch (error) {
  console.error('\n❌ Erro durante o build:', error.message);
  process.exit(1);
}
