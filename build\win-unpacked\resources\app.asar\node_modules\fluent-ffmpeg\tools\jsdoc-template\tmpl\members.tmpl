<?js
var data = obj;
var self = this;
?>
<dt>
    <h4 class="name" id="<?js= id ?>"><?js= data.attribs + name + (data.signature ? data.signature : '') ?></h4>

    <?js if (data.summary) { ?>
    <p class="summary"><?js= summary ?></p>
    <?js } ?>
</dt>
<dd>
    <?js if (data.description) { ?>
    <div class="description">
        <?js= data.description ?>
    </div>
    <?js } ?>

    <?js if (data.type && data.type.names) {?>
        <h5>Type:</h5>
        <ul>
            <li>
                <?js= self.partial('type.tmpl', data.type.names) ?>
            </li>
        </ul>
    <?js } ?>

    <?js= this.partial('details.tmpl', data) ?>

    <?js if (data.fires && fires.length) { ?>
        <h5>Fires:</h5>
        <ul><?js fires.forEach(function(f) { ?>
            <li><?js= self.linkto(f) ?></li>
        <?js }); ?></ul>
    <?js } ?>

    <?js if (data.examples && examples.length) { ?>
        <h5>Example<?js= examples.length > 1? 's':'' ?></h5>
        <?js= this.partial('examples.tmpl', examples) ?>
    <?js } ?>
</dd>
