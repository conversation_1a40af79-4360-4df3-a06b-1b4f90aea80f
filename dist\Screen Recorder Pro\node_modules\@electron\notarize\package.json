{"name": "@electron/notarize", "version": "2.2.1", "description": "Notarize your Electron app", "main": "lib/index.js", "typings": "lib/index.d.ts", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/electron/notarize#readme", "repository": {"type": "git", "url": "https://github.com/electron/notarize.git"}, "bugs": {"url": "https://github.com/electron/notarize/issues"}, "scripts": {"build": "tsc", "lint": "prettier --check \"src/**/*.ts\"", "prepare": "yarn build", "test": "jest"}, "files": ["lib"], "engines": {"node": ">= 10.0.0"}, "devDependencies": {"@types/debug": "^4.1.5", "@types/fs-extra": "^9.0.1", "@types/jest": "^29.0.0", "@types/node": "^13.7.7", "@types/promise-retry": "^1.1.3", "jest": "^29.0.0", "prettier": "^1.18.2", "ts-jest": "^29.0.0", "typescript": "^4.8.4"}, "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.1", "promise-retry": "^2.0.1"}}