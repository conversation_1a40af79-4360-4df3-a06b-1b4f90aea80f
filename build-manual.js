const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Iniciando build manual do Sistema de Gravação...\n');

// Função para copiar arquivos recursivamente
function copyRecursive(src, dest) {
  const stats = fs.statSync(src);
  
  if (stats.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    for (const file of files) {
      copyRecursive(path.join(src, file), path.join(dest, file));
    }
  } else {
    const destDir = path.dirname(dest);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    fs.copyFileSync(src, dest);
  }
}

// Função para limpar diretório
function cleanDir(dir) {
  if (fs.existsSync(dir)) {
    try {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ Limpou diretório: ${dir}`);
    } catch (error) {
      console.log(`⚠️ Não foi possível limpar ${dir}: ${error.message}`);
    }
  }
}

try {
  // 1. Limpar diretórios de build
  console.log('🧹 Limpando diretórios de build...');
  cleanDir('./build-manual');
  cleanDir('./build');
  
  // 2. Criar estrutura de diretórios
  console.log('📁 Criando estrutura de diretórios...');
  const buildDir = './build-manual';
  const appDir = path.join(buildDir, 'Sistema de Gravação');
  const resourcesDir = path.join(appDir, 'resources');
  
  fs.mkdirSync(appDir, { recursive: true });
  fs.mkdirSync(resourcesDir, { recursive: true });
  
  // 3. Copiar código fonte
  console.log('📋 Copiando código fonte...');
  copyRecursive('./src', path.join(appDir, 'src'));

  // Substituir main.js pela versão simplificada
  const mainSimplePath = path.join(appDir, 'src', 'main.js');
  const mainSimpleSource = './src/main-simple.js';

  if (fs.existsSync(mainSimpleSource)) {
    fs.copyFileSync(mainSimpleSource, mainSimplePath);
    console.log('  ✅ Usando versão simplificada do main.js');
  }
  
  // 4. Copiar package.json
  console.log('📦 Copiando package.json...');
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  // Remover devDependencies para reduzir tamanho
  delete packageJson.devDependencies;
  fs.writeFileSync(path.join(appDir, 'package.json'), JSON.stringify(packageJson, null, 2));
  
  // 5. Copiar node_modules essenciais
  console.log('📚 Copiando dependências essenciais...');
  const essentialModules = [
    'fluent-ffmpeg',
    '@ffmpeg-installer',
    'ffmpeg-static'
  ];
  
  const nodeModulesDir = path.join(appDir, 'node_modules');
  fs.mkdirSync(nodeModulesDir, { recursive: true });
  
  for (const module of essentialModules) {
    const srcPath = path.join('./node_modules', module);
    const destPath = path.join(nodeModulesDir, module);
    
    if (fs.existsSync(srcPath)) {
      console.log(`  📦 Copiando ${module}...`);
      copyRecursive(srcPath, destPath);
    } else {
      console.log(`  ⚠️ Módulo ${module} não encontrado`);
    }
  }
  
  // 6. Copiar FFmpeg
  console.log('🎬 Copiando FFmpeg...');
  const ffmpegPaths = [
    {
      src: './node_modules/@ffmpeg-installer/win32-x64/ffmpeg.exe',
      dest: path.join(resourcesDir, 'ffmpeg.exe')
    },
    {
      src: './node_modules/ffmpeg-static/ffmpeg.exe',
      dest: path.join(resourcesDir, 'ffmpeg-static.exe')
    }
  ];
  
  for (const { src, dest } of ffmpegPaths) {
    if (fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`  ✅ Copiado: ${path.basename(dest)}`);
    } else {
      console.log(`  ❌ Não encontrado: ${src}`);
    }
  }
  
  // 7. Criar script de inicialização
  console.log('🚀 Criando script de inicialização...');
  const startScript = `@echo off
echo Iniciando Sistema de Gravação...
cd /d "%~dp0"
node src/main.js
pause`;
  
  fs.writeFileSync(path.join(appDir, 'Iniciar.bat'), startScript);
  
  // 8. Criar README
  console.log('📄 Criando README...');
  const readme = `# Sistema de Gravação - Build Manual

## Como usar:
1. Execute "Iniciar.bat" para iniciar o aplicativo
2. Certifique-se de ter o Node.js instalado no sistema

## Arquivos incluídos:
- src/ - Código fonte da aplicação
- resources/ - FFmpeg embutido
- node_modules/ - Dependências essenciais
- package.json - Configuração do projeto

## FFmpeg:
O FFmpeg está incluído na pasta resources/ e será detectado automaticamente.

Versão: ${packageJson.version}
Autor: ${packageJson.author}
`;
  
  fs.writeFileSync(path.join(appDir, 'README.txt'), readme);
  
  // 9. Verificar build
  console.log('\n🔍 Verificando build...');
  const ffmpegExists = fs.existsSync(path.join(resourcesDir, 'ffmpeg.exe'));
  const srcExists = fs.existsSync(path.join(appDir, 'src', 'main.js'));
  const packageExists = fs.existsSync(path.join(appDir, 'package.json'));
  
  console.log(`  FFmpeg: ${ffmpegExists ? '✅' : '❌'}`);
  console.log(`  Código fonte: ${srcExists ? '✅' : '❌'}`);
  console.log(`  Package.json: ${packageExists ? '✅' : '❌'}`);
  
  if (ffmpegExists && srcExists && packageExists) {
    console.log('\n🎉 Build manual concluído com sucesso!');
    console.log(`📁 Localização: ${path.resolve(appDir)}`);
    console.log('\n💡 Para usar:');
    console.log('1. Navegue até a pasta do build');
    console.log('2. Execute "Iniciar.bat"');
    console.log('\n📦 Para distribuir:');
    console.log('1. Comprima a pasta "Sistema de Gravação" em um ZIP');
    console.log('2. Distribua o arquivo ZIP');
    console.log('3. O usuário deve ter Node.js instalado');
  } else {
    console.log('\n❌ Build incompleto. Verifique os erros acima.');
  }
  
} catch (error) {
  console.error('\n❌ Erro durante o build:', error.message);
  process.exit(1);
}
