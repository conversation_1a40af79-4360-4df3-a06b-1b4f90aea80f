{"name": "@ffmpeg/util", "version": "0.12.2", "description": "browser utils for @ffmpeg/*", "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/ffmpegwasm/ffmpeg.wasm.git"}, "keywords": ["ffmpeg", "video", "audio", "transcode"], "author": "<PERSON> <jerome<PERSON><EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ffmpegwasm/ffmpeg.wasm/issues"}, "engines": {"node": ">=18.x"}, "homepage": "https://github.com/ffmpegwasm/ffmpeg.wasm#readme", "publishConfig": {"access": "public"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "eslint": "^8.45.0", "rimraf": "^5.0.1", "typescript": "^5.1.6", "webpack-cli": "^5.1.4"}, "scripts": {"dev": "tsc -p tsconfig-esm.json --watch", "lint": "eslint src", "clean": "<PERSON><PERSON><PERSON> dist", "build:esm": "tsc -p tsconfig.esm.json", "build:umd": "tsc -p tsconfig.cjs.json && webpack", "build": "npm run clean && npm run build:esm && npm run build:umd"}}