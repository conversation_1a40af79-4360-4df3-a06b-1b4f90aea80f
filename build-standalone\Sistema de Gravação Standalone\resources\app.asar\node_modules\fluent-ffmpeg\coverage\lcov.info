TN:
SF:src/capabilities.ts
FN:16,<instance_members_initializer>
FN:23,getLines
FN:32,codecs
FN:42,formats
FN:50,filters
FN:58,encoders
FN:66,decoders
FNF:7
FNH:4
FNDA:2,<instance_members_initializer>
FNDA:2,getLines
FNDA:2,codecs
FNDA:2,formats
FNDA:0,filters
FNDA:0,encoders
FNDA:0,decoders
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,2
DA:17,2
DA:18,2
DA:19,2
DA:20,2
DA:21,2
DA:22,2
DA:23,2
DA:24,2
DA:25,2
DA:26,2
DA:27,2
DA:28,2
DA:29,2
DA:30,2
DA:31,2
DA:32,2
DA:33,2
DA:34,2
DA:35,2
DA:36,1
DA:37,1
DA:38,2
DA:39,2
DA:40,2
DA:41,2
DA:42,2
DA:43,2
DA:44,1
DA:45,1
DA:46,2
DA:47,2
DA:48,2
DA:49,2
DA:50,2
DA:51,0
DA:52,0
DA:53,0
DA:54,0
DA:55,0
DA:56,0
DA:57,2
DA:58,2
DA:59,0
DA:60,0
DA:61,0
DA:62,0
DA:63,0
DA:64,0
DA:65,2
DA:66,2
DA:67,0
DA:68,0
DA:69,0
DA:70,0
DA:71,0
DA:72,0
DA:73,2
LF:73
LH:55
BRDA:16,0,0,2
BRDA:23,1,0,2
BRDA:29,2,0,0
BRDA:32,3,0,2
BRDA:35,4,0,1
BRDA:42,5,0,2
BRDA:43,6,0,1
BRF:7
BRH:6
end_of_record
TN:
SF:src/command.ts
FN:16,<instance_members_initializer>
FN:22,FfmpegCommand
FN:53,#validateIO
FN:63,getFfmpegArguments
FN:88,run
FNF:5
FNH:4
FNDA:5,<instance_members_initializer>
FNDA:5,FfmpegCommand
FNDA:4,#validateIO
FNDA:3,getFfmpegArguments
FNDA:0,run
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,5
DA:17,5
DA:18,5
DA:19,5
DA:20,5
DA:21,5
DA:22,5
DA:23,5
DA:24,3
DA:25,1
DA:26,1
DA:27,2
DA:28,2
DA:29,2
DA:30,4
DA:31,5
DA:32,0
DA:33,0
DA:34,0
DA:35,0
DA:36,0
DA:37,0
DA:38,4
DA:39,5
DA:40,5
DA:41,5
DA:42,5
DA:43,5
DA:44,5
DA:45,5
DA:46,5
DA:47,5
DA:48,5
DA:49,5
DA:50,5
DA:51,5
DA:52,5
DA:53,5
DA:54,4
DA:55,1
DA:56,1
DA:57,3
DA:58,4
DA:59,0
DA:60,0
DA:61,4
DA:62,5
DA:63,5
DA:64,3
DA:65,3
DA:66,3
DA:67,5
DA:68,5
DA:69,3
DA:70,3
DA:71,3
DA:72,3
DA:73,0
DA:74,0
DA:75,3
DA:76,3
DA:77,0
DA:78,0
DA:79,0
DA:80,3
DA:81,3
DA:82,0
DA:83,0
DA:84,3
DA:85,3
DA:86,3
DA:87,5
DA:88,5
DA:89,0
DA:90,0
DA:91,0
DA:92,0
DA:93,0
DA:94,0
DA:95,0
DA:96,0
DA:97,0
DA:98,0
DA:99,0
DA:100,0
DA:101,0
DA:102,0
DA:103,0
DA:104,0
DA:105,0
DA:106,5
LF:106
LH:74
BRDA:16,0,0,5
BRDA:22,1,0,5
BRDA:23,2,0,3
BRDA:24,3,0,1
BRDA:27,4,0,2
BRDA:30,5,0,4
BRDA:31,6,0,0
BRDA:38,7,0,4
BRDA:39,8,0,0
BRDA:43,9,0,4
BRDA:48,10,0,4
BRDA:40,11,0,7
BRDA:53,12,0,4
BRDA:54,13,0,1
BRDA:57,14,0,3
BRDA:58,15,0,0
BRDA:54,16,0,7
BRDA:63,17,0,3
BRDA:66,18,0,5
BRDA:72,19,0,0
BRDA:76,20,0,0
BRDA:81,21,0,0
BRF:22
BRH:16
end_of_record
TN:
SF:src/input.ts
FN:18,<instance_members_initializer>
FN:26,FfmpegInput
FN:39,get isStream
FN:43,get isLocalFile
FN:52,#getSourceString
FN:60,#getOptions
FN:84,getFfmpegArguments
FNF:7
FNH:7
FNDA:21,<instance_members_initializer>
FNDA:21,FfmpegInput
FNDA:15,get isStream
FNDA:8,get isLocalFile
FNDA:11,#getSourceString
FNDA:11,#getOptions
FNDA:11,getFfmpegArguments
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,21
DA:19,21
DA:20,21
DA:21,21
DA:22,21
DA:23,21
DA:24,21
DA:25,21
DA:26,21
DA:27,21
DA:28,12
DA:29,12
DA:30,21
DA:31,21
DA:32,21
DA:33,21
DA:34,21
DA:35,21
DA:36,21
DA:37,21
DA:38,21
DA:39,21
DA:40,15
DA:41,15
DA:42,21
DA:43,21
DA:44,8
DA:45,2
DA:46,8
DA:47,6
DA:48,6
DA:49,6
DA:50,8
DA:51,21
DA:52,21
DA:53,11
DA:54,8
DA:55,11
DA:56,3
DA:57,3
DA:58,11
DA:59,21
DA:60,21
DA:61,11
DA:62,11
DA:63,11
DA:64,2
DA:65,2
DA:66,11
DA:67,11
DA:68,1
DA:69,11
DA:70,2
DA:71,2
DA:72,11
DA:73,11
DA:74,1
DA:75,1
DA:76,11
DA:77,11
DA:78,1
DA:79,1
DA:80,11
DA:81,11
DA:82,11
DA:83,21
DA:84,21
DA:85,11
DA:86,11
DA:87,11
DA:88,11
DA:89,11
DA:90,11
DA:91,11
DA:92,21
LF:92
LH:92
BRDA:18,0,0,21
BRDA:26,1,0,21
BRDA:27,2,0,14
BRDA:27,3,0,12
BRDA:39,4,0,15
BRDA:43,5,0,8
BRDA:44,6,0,6
BRDA:44,7,0,2
BRDA:46,8,0,6
BRDA:48,9,0,4
BRDA:52,10,0,11
BRDA:53,11,0,8
BRDA:55,12,0,3
BRDA:60,13,0,11
BRDA:63,14,0,2
BRDA:67,15,0,1
BRDA:69,16,0,10
BRDA:69,17,0,2
BRDA:73,18,0,1
BRDA:77,19,0,1
BRDA:84,20,0,11
BRF:21
BRH:21
end_of_record
TN:
SF:src/main.ts
FN:1,(empty-report)
FNF:1
FNH:0
FNDA:0,(empty-report)
DA:1,0
DA:2,0
DA:3,0
DA:4,0
DA:5,0
DA:6,0
DA:7,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:12,0
DA:13,0
DA:14,0
DA:15,0
DA:16,0
DA:17,0
DA:18,0
DA:19,0
DA:20,0
DA:21,0
DA:22,0
DA:23,0
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:28,0
DA:29,0
DA:30,0
DA:31,0
DA:32,0
LF:32
LH:0
BRDA:1,0,0,0
BRF:1
BRH:0
end_of_record
TN:
SF:src/output.ts
FN:41,<instance_members_initializer>
FN:52,FfmpegOutput
FN:68,get isStream
FN:72,get isLocalFile
FN:81,#getAudioOptions
FN:115,#getVideoOptions
FN:159,#getOptions
FN:181,#getOutputString
FN:189,getFfmpegArguments
FNF:9
FNH:9
FNDA:18,<instance_members_initializer>
FNDA:18,FfmpegOutput
FNDA:8,get isStream
FNDA:8,get isLocalFile
FNDA:10,#getAudioOptions
FNDA:10,#getVideoOptions
FNDA:10,#getOptions
FNDA:10,#getOutputString
FNDA:10,getFfmpegArguments
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:36,1
DA:37,1
DA:38,1
DA:39,1
DA:40,1
DA:41,18
DA:42,18
DA:43,18
DA:44,18
DA:45,18
DA:46,18
DA:47,18
DA:48,18
DA:49,18
DA:50,18
DA:51,18
DA:52,18
DA:53,18
DA:54,5
DA:55,5
DA:56,18
DA:57,18
DA:58,18
DA:59,18
DA:60,18
DA:61,18
DA:62,18
DA:63,18
DA:64,18
DA:65,18
DA:66,18
DA:67,18
DA:68,18
DA:69,8
DA:70,8
DA:71,18
DA:72,18
DA:73,8
DA:74,2
DA:75,8
DA:76,6
DA:77,6
DA:78,6
DA:79,8
DA:80,18
DA:81,18
DA:82,10
DA:83,10
DA:84,10
DA:85,1
DA:86,10
DA:87,1
DA:88,1
DA:89,1
DA:90,1
DA:91,1
DA:92,1
DA:93,1
DA:94,1
DA:95,1
DA:96,1
DA:97,1
DA:98,1
DA:99,1
DA:100,1
DA:101,1
DA:102,1
DA:103,1
DA:104,1
DA:105,1
DA:106,1
DA:107,1
DA:108,0
DA:109,0
DA:110,1
DA:111,10
DA:112,10
DA:113,10
DA:114,18
DA:115,18
DA:116,10
DA:117,10
DA:118,10
DA:119,1
DA:120,10
DA:121,2
DA:122,1
DA:123,1
DA:124,2
DA:125,2
DA:126,2
DA:127,2
DA:128,2
DA:129,2
DA:130,1
DA:131,1
DA:132,1
DA:133,1
DA:134,1
DA:135,1
DA:136,1
DA:137,1
DA:138,1
DA:139,2
DA:140,2
DA:141,2
DA:142,1
DA:143,1
DA:144,2
DA:145,2
DA:146,1
DA:147,1
DA:148,2
DA:149,2
DA:150,0
DA:151,0
DA:152,2
DA:153,2
DA:154,2
DA:155,10
DA:156,10
DA:157,10
DA:158,18
DA:159,18
DA:160,10
DA:161,10
DA:162,10
DA:163,1
DA:164,1
DA:165,10
DA:166,10
DA:167,1
DA:168,1
DA:169,10
DA:170,10
DA:171,2
DA:172,2
DA:173,10
DA:174,10
DA:175,1
DA:176,1
DA:177,10
DA:178,10
DA:179,10
DA:180,18
DA:181,18
DA:182,10
DA:183,9
DA:184,10
DA:185,1
DA:186,1
DA:187,10
DA:188,18
DA:189,18
DA:190,10
DA:191,10
DA:192,10
DA:193,10
DA:194,10
DA:195,10
DA:196,10
DA:197,10
DA:198,18
LF:198
LH:194
BRDA:41,0,0,18
BRDA:52,1,0,18
BRDA:53,2,0,14
BRDA:53,3,0,5
BRDA:68,4,0,8
BRDA:72,5,0,8
BRDA:73,6,0,2
BRDA:75,7,0,6
BRDA:77,8,0,4
BRDA:81,9,0,10
BRDA:84,10,0,1
BRDA:86,11,0,9
BRDA:86,12,0,1
BRDA:107,13,0,0
BRDA:115,14,0,10
BRDA:118,15,0,1
BRDA:120,16,0,9
BRDA:120,17,0,2
BRDA:121,18,0,1
BRDA:129,19,0,1
BRDA:141,20,0,1
BRDA:145,21,0,1
BRDA:149,22,0,0
BRDA:159,23,0,10
BRDA:162,24,0,1
BRDA:166,25,0,1
BRDA:170,26,0,2
BRDA:174,27,0,1
BRDA:181,28,0,10
BRDA:182,29,0,9
BRDA:184,30,0,1
BRDA:189,31,0,10
BRF:32
BRH:30
end_of_record
TN:
SF:src/process.ts
FN:34,<instance_members_initializer>
FN:46,FfmpegProcess
FN:61,#validateOptions
FN:70,run
FNF:4
FNH:4
FNDA:16,<instance_members_initializer>
FNDA:16,FfmpegProcess
FNDA:16,#validateOptions
FNDA:16,run
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:34,16
DA:35,16
DA:36,16
DA:37,16
DA:38,16
DA:39,16
DA:40,16
DA:41,16
DA:42,16
DA:43,16
DA:44,16
DA:45,16
DA:46,16
DA:47,16
DA:48,16
DA:49,16
DA:50,16
DA:51,16
DA:52,16
DA:53,16
DA:54,16
DA:55,16
DA:56,16
DA:57,16
DA:58,16
DA:59,16
DA:60,16
DA:61,16
DA:62,16
DA:63,0
DA:64,0
DA:65,0
DA:66,0
DA:67,16
DA:68,16
DA:69,16
DA:70,16
DA:71,16
DA:72,16
DA:73,16
DA:74,16
DA:75,16
DA:76,16
DA:77,1
DA:78,1
DA:79,1
DA:80,16
DA:81,16
DA:82,16
DA:83,16
DA:84,16
DA:85,16
DA:86,16
DA:87,16
DA:88,16
DA:89,16
DA:90,16
DA:91,16
DA:92,1
DA:93,1
DA:94,16
DA:95,16
DA:96,1
DA:97,5
DA:98,5
DA:99,2
DA:100,2
DA:101,1
DA:102,1
DA:103,16
DA:104,16
DA:105,1
DA:106,1
DA:107,11
DA:108,7
DA:109,7
DA:110,1
DA:111,1
DA:112,16
DA:113,16
DA:114,16
DA:115,16
DA:116,14
DA:117,14
DA:118,14
DA:119,14
DA:120,1
DA:121,14
DA:122,2
DA:123,2
DA:124,2
DA:125,2
DA:126,1
DA:127,1
DA:128,2
DA:129,2
DA:130,13
DA:131,11
DA:132,11
DA:133,11
DA:134,11
DA:135,11
DA:136,16
DA:137,16
DA:138,16
DA:139,0
DA:140,0
DA:141,0
DA:142,0
DA:143,0
DA:144,0
DA:145,0
DA:146,0
DA:147,0
DA:148,0
DA:149,0
DA:150,16
DA:151,16
DA:152,16
DA:153,16
DA:154,0
DA:155,0
DA:156,0
DA:157,0
DA:158,0
DA:159,0
DA:160,0
DA:161,0
DA:162,16
DA:163,1
DA:164,1
DA:165,16
DA:166,16
DA:167,16
DA:168,2
DA:169,2
DA:170,2
DA:171,2
DA:172,2
DA:173,16
DA:174,16
DA:175,16
DA:176,16
LF:176
LH:153
BRDA:34,0,0,16
BRDA:46,1,0,16
BRDA:61,2,0,16
BRDA:62,3,0,0
BRDA:62,4,0,0
BRDA:70,5,0,16
BRDA:71,6,0,15
BRDA:76,7,0,1
BRDA:76,8,0,1
BRDA:76,9,0,1
BRDA:167,10,0,2
BRDA:81,11,0,16
BRDA:91,12,0,1
BRDA:95,13,0,1
BRDA:104,14,0,1
BRDA:138,15,0,0
BRDA:153,16,0,0
BRDA:162,17,0,1
BRDA:96,18,0,5
BRDA:98,19,0,2
BRDA:106,20,0,11
BRDA:107,21,0,7
BRDA:113,22,0,2
BRDA:115,23,0,14
BRDA:119,24,0,1
BRDA:121,25,0,13
BRDA:121,26,0,2
BRDA:125,27,0,1
BRDA:130,28,0,11
BRDA:151,29,0,22
BRDA:163,30,0,2
BRDA:169,31,0,1
BRDA:170,32,0,1
BRF:33
BRH:29
end_of_record
TN:
SF:src/utils/filters.ts
FN:18,escapeOption
FN:26,escapeFilter
FN:34,generateFilter
FN:75,generateFilterChain
FN:79,generateFilterGraph
FNF:5
FNH:5
FNDA:10,escapeOption
FNDA:7,escapeFilter
FNDA:14,generateFilter
FNDA:3,generateFilterChain
FNDA:1,generateFilterGraph
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,10
DA:19,10
DA:20,6
DA:21,6
DA:22,4
DA:23,4
DA:24,4
DA:25,1
DA:26,7
DA:27,7
DA:28,2
DA:29,2
DA:30,5
DA:31,5
DA:32,5
DA:33,1
DA:34,14
DA:35,14
DA:36,14
DA:37,14
DA:38,14
DA:39,14
DA:40,14
DA:41,2
DA:42,2
DA:43,14
DA:44,14
DA:45,5
DA:46,5
DA:47,5
DA:48,2
DA:49,2
DA:50,2
DA:51,5
DA:52,3
DA:53,3
DA:54,5
DA:55,5
DA:56,5
DA:57,14
DA:58,14
DA:59,7
DA:60,7
DA:61,14
DA:62,14
DA:63,3
DA:64,3
DA:65,14
DA:66,14
DA:67,3
DA:68,3
DA:69,3
DA:70,3
DA:71,14
DA:72,14
DA:73,14
DA:74,1
DA:75,3
DA:76,3
DA:77,3
DA:78,1
DA:79,1
DA:80,1
DA:81,1
LF:81
LH:81
BRDA:18,0,0,10
BRDA:19,1,0,6
BRDA:22,2,0,4
BRDA:26,3,0,7
BRDA:27,4,0,2
BRDA:30,5,0,5
BRDA:34,6,0,14
BRDA:40,7,0,2
BRDA:44,8,0,5
BRDA:47,9,0,2
BRDA:51,10,0,3
BRDA:58,11,0,7
BRDA:62,12,0,3
BRDA:66,13,0,3
BRDA:49,14,0,5
BRDA:52,15,0,5
BRDA:63,16,0,7
BRDA:68,17,0,7
BRDA:75,18,0,3
BRDA:76,19,0,7
BRDA:79,20,0,1
BRDA:80,21,0,2
BRF:22
BRH:22
end_of_record
TN:
SF:src/utils/formatting.ts
FN:1,formatBitrate
FNF:1
FNH:1
FNDA:6,formatBitrate
DA:1,6
DA:2,6
DA:3,2
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,6
DA:10,4
DA:11,4
DA:12,6
LF:12
LH:12
BRDA:1,0,0,6
BRDA:2,1,0,2
BRDA:3,2,0,1
BRDA:9,3,0,4
BRF:4
BRH:4
end_of_record
TN:
SF:src/utils/line-buffer.ts
FN:5,<instance_members_initializer>
FN:10,LineBuffer
FN:18,append
FN:45,close
FN:58,toString
FNF:5
FNH:5
FNDA:34,<instance_members_initializer>
FNDA:34,LineBuffer
FNDA:30,append
FNDA:31,close
FNDA:23,toString
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,34
DA:6,34
DA:7,34
DA:8,34
DA:9,34
DA:10,34
DA:11,34
DA:12,34
DA:13,34
DA:14,34
DA:15,34
DA:16,34
DA:17,34
DA:18,34
DA:19,30
DA:20,1
DA:21,1
DA:22,29
DA:23,29
DA:24,29
DA:25,29
DA:26,29
DA:27,2
DA:28,2
DA:29,29
DA:30,27
DA:31,5
DA:32,5
DA:33,5
DA:34,5
DA:35,27
DA:36,27
DA:37,27
DA:38,26
DA:39,26
DA:40,26
DA:41,27
DA:42,29
DA:43,30
DA:44,34
DA:45,34
DA:46,31
DA:47,1
DA:48,1
DA:49,30
DA:50,31
DA:51,5
DA:52,5
DA:53,5
DA:54,30
DA:55,30
DA:56,30
DA:57,34
DA:58,34
DA:59,23
DA:60,23
DA:61,34
LF:61
LH:61
BRDA:5,0,0,34
BRDA:10,1,0,34
BRDA:18,2,0,30
BRDA:19,3,0,1
BRDA:22,4,0,29
BRDA:26,5,0,2
BRDA:29,6,0,27
BRDA:30,7,0,5
BRDA:37,8,0,26
BRDA:45,9,0,31
BRDA:46,10,0,1
BRDA:49,11,0,30
BRDA:50,12,0,5
BRDA:54,13,0,30
BRDA:58,14,0,23
BRF:15
BRH:15
end_of_record
TN:
SF:src/utils/parsing.ts
FN:36,extractErrorMessage
FN:56,extractProgress
FN:86,<instance_members_initializer>
FN:93,CodecDataExtractor
FN:102,processLine
FN:149,parseCodecType
FN:157,extractCodecs
FN:203,extractFormats
FN:221,parseFilterStreams
FN:233,extractFilters
FN:253,parseEncoderType
FN:259,extractEncoders
FNF:12
FNH:8
FNDA:1,extractErrorMessage
FNDA:7,extractProgress
FNDA:2,<instance_members_initializer>
FNDA:2,CodecDataExtractor
FNDA:18,processLine
FNDA:5,parseCodecType
FNDA:1,extractCodecs
FNDA:1,extractFormats
FNDA:0,parseFilterStreams
FNDA:0,extractFilters
FNDA:0,parseEncoderType
FNDA:0,extractEncoders
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:36,1
DA:37,1
DA:38,1
DA:39,1
DA:40,7
DA:41,3
DA:42,7
DA:43,4
DA:44,4
DA:45,4
DA:46,1
DA:47,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:53,1
DA:54,1
DA:55,1
DA:56,7
DA:57,7
DA:58,7
DA:59,7
DA:60,7
DA:61,7
DA:62,7
DA:63,21
DA:64,21
DA:65,21
DA:66,4
DA:67,4
DA:68,4
DA:69,17
DA:70,21
DA:71,6
DA:72,21
DA:73,3
DA:74,11
DA:75,2
DA:76,8
DA:77,3
DA:78,3
DA:79,3
DA:80,3
DA:81,21
DA:82,3
DA:83,3
DA:84,3
DA:85,1
DA:86,2
DA:87,2
DA:88,2
DA:89,2
DA:90,2
DA:91,2
DA:92,2
DA:93,2
DA:94,2
DA:95,2
DA:96,2
DA:97,2
DA:98,2
DA:99,2
DA:100,2
DA:101,2
DA:102,2
DA:103,18
DA:104,18
DA:105,2
DA:106,2
DA:107,2
DA:108,2
DA:109,2
DA:110,2
DA:111,2
DA:112,2
DA:113,16
DA:114,18
DA:115,13
DA:116,13
DA:117,2
DA:118,2
DA:119,2
DA:120,11
DA:121,11
DA:122,13
DA:123,2
DA:124,2
DA:125,2
DA:126,2
DA:127,9
DA:128,9
DA:129,13
DA:130,2
DA:131,2
DA:132,2
DA:133,2
DA:134,13
DA:135,10
DA:136,18
DA:137,1
DA:138,1
DA:139,10
DA:140,18
DA:141,3
DA:142,3
DA:143,3
DA:144,3
DA:145,3
DA:146,18
DA:147,2
DA:148,1
DA:149,5
DA:150,5
DA:151,5
DA:152,5
DA:153,5
DA:154,1
DA:155,1
DA:156,1
DA:157,1
DA:158,1
DA:159,1
DA:160,1
DA:161,7
DA:162,7
DA:163,5
DA:164,5
DA:165,5
DA:166,5
DA:167,5
DA:168,5
DA:169,5
DA:170,5
DA:171,5
DA:172,5
DA:173,5
DA:174,5
DA:175,5
DA:176,5
DA:177,3
DA:178,3
DA:179,1
DA:180,1
DA:181,1
DA:182,1
DA:183,1
DA:184,3
DA:185,5
DA:186,5
DA:187,3
DA:188,3
DA:189,1
DA:190,1
DA:191,1
DA:192,1
DA:193,1
DA:194,3
DA:195,5
DA:196,5
DA:197,5
DA:198,7
DA:199,1
DA:200,1
DA:201,1
DA:202,1
DA:203,1
DA:204,1
DA:205,1
DA:206,1
DA:207,5
DA:208,5
DA:209,3
DA:210,3
DA:211,3
DA:212,3
DA:213,3
DA:214,3
DA:215,3
DA:216,5
DA:217,1
DA:218,1
DA:219,1
DA:220,1
DA:221,0
DA:222,0
DA:223,0
DA:224,0
DA:225,0
DA:226,0
DA:227,0
DA:228,0
DA:229,0
DA:230,0
DA:231,0
DA:232,1
DA:233,0
DA:234,0
DA:235,0
DA:236,0
DA:237,0
DA:238,0
DA:239,0
DA:240,0
DA:241,0
DA:242,0
DA:243,0
DA:244,0
DA:245,0
DA:246,0
DA:247,0
DA:248,0
DA:249,0
DA:250,0
DA:251,0
DA:252,1
DA:253,0
DA:254,0
DA:255,0
DA:256,0
DA:257,0
DA:258,1
DA:259,0
DA:260,0
DA:261,0
DA:262,0
DA:263,0
DA:264,0
DA:265,0
DA:266,0
DA:267,0
DA:268,0
DA:269,0
DA:270,0
DA:271,0
DA:272,0
DA:273,0
DA:274,0
DA:275,0
DA:276,0
DA:277,0
DA:278,0
DA:279,0
DA:280,0
LF:280
LH:223
BRDA:36,0,0,1
BRDA:39,1,0,7
BRDA:40,2,0,6
BRDA:40,3,0,3
BRDA:42,4,0,4
BRDA:56,5,0,7
BRDA:62,6,0,21
BRDA:65,7,0,4
BRDA:69,8,0,17
BRDA:70,9,0,14
BRDA:70,10,0,6
BRDA:72,11,0,11
BRDA:72,12,0,3
BRDA:74,13,0,8
BRDA:74,14,0,2
BRDA:76,15,0,6
BRDA:76,16,0,3
BRDA:82,17,0,3
BRDA:86,18,0,2
BRDA:93,19,0,2
BRDA:102,20,0,18
BRDA:104,21,0,2
BRDA:113,22,0,16
BRDA:114,23,0,13
BRDA:116,24,0,2
BRDA:120,25,0,11
BRDA:122,26,0,2
BRDA:127,27,0,9
BRDA:129,28,0,2
BRDA:135,29,0,10
BRDA:136,30,0,1
BRDA:139,31,0,10
BRDA:140,32,0,3
BRDA:149,33,0,5
BRDA:150,34,0,1
BRDA:151,35,0,4
BRDA:151,36,0,1
BRDA:152,37,0,3
BRDA:152,38,0,1
BRDA:153,39,0,2
BRDA:153,40,0,1
BRDA:157,41,0,1
BRDA:160,42,0,7
BRDA:162,43,0,5
BRDA:176,44,0,3
BRDA:178,45,0,1
BRDA:186,46,0,3
BRDA:188,47,0,1
BRDA:203,48,0,1
BRDA:206,49,0,5
BRDA:208,50,0,3
BRF:51
BRH:51
end_of_record
TN:
SF:src/utils/platform.ts
FNF:0
FNH:0
DA:1,1
DA:2,1
DA:3,1
LF:3
LH:3
BRF:0
BRH:0
end_of_record
TN:
SF:src/utils/regexp.ts
FNF:0
FNH:0
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
LF:20
LH:20
BRF:0
BRH:0
end_of_record
TN:
SF:tests/acceptance/dummy.ts
FNF:0
FNH:0
DA:1,1
DA:2,1
DA:3,1
LF:3
LH:3
BRDA:3,0,0,1
BRF:1
BRH:1
end_of_record
TN:
SF:tests/helpers/async.ts
FN:1,delay
FNF:1
FNH:1
FNDA:22,delay
DA:1,22
DA:2,22
DA:3,22
LF:3
LH:3
BRDA:1,0,0,22
BRDA:2,1,0,22
BRF:2
BRH:2
end_of_record
TN:
SF:tests/helpers/spawn-stub.ts
FN:10,<instance_members_initializer>
FN:14,FakeProcess
FN:23,stubSpawn
FNF:3
FNH:3
FNDA:16,<instance_members_initializer>
FNDA:16,FakeProcess
FNDA:16,stubSpawn
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,16
DA:11,16
DA:12,16
DA:13,16
DA:14,16
DA:15,16
DA:16,16
DA:17,16
DA:18,16
DA:19,16
DA:20,16
DA:21,16
DA:22,1
DA:23,16
DA:24,16
DA:25,16
DA:26,16
DA:27,16
DA:28,16
DA:29,16
DA:30,16
DA:31,16
DA:32,16
DA:33,16
DA:34,16
LF:34
LH:34
BRDA:10,0,0,16
BRDA:14,1,0,16
BRDA:18,2,0,16
BRDA:23,3,0,16
BRDA:31,4,0,16
BRF:5
BRH:5
end_of_record
TN:
SF:tests/helpers/streams.ts
FN:4,_read
FN:8,_write
FNF:2
FNH:0
FNDA:0,_read
FNDA:0,_write
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
LF:9
LH:9
BRF:0
BRH:0
end_of_record
