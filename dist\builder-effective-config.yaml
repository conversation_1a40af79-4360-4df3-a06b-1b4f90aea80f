directories:
  output: dist
  buildResources: build
appId: com.sistemadegravacao.app
productName: Sistema de Gravação
files:
  - filter:
      - src/**/*
      - node_modules/**/*
      - package.json
      - '!node_modules/electron/**/*'
      - '!node_modules/electron-builder/**/*'
extraResources:
  - from: node_modules/@ffmpeg-installer/win32-x64/ffmpeg.exe
    to: ffmpeg.exe
    filter:
      - '**/*'
  - from: node_modules/ffmpeg-static/ffmpeg.exe
    to: ffmpeg-static.exe
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  requestedExecutionLevel: asInvoker
mac:
  target: dmg
  icon: src/assets/icon.icns
linux:
  target: AppImage
  icon: src/assets/icon.png
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
portable:
  artifactName: ${productName}-${version}-portable.${ext}
electronVersion: 28.3.3
