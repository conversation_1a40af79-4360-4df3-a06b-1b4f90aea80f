{"name": "screen-recorder-pro", "version": "1.0.0", "description": "Gravador de tela profissional com captura seletiva e compressão", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "pack": "electron-builder --dir", "build:manual": "node build-manual.js", "build:final": "node build-final.js", "test-ffmpeg": "node test-ffmpeg.js", "postinstall": "electron-builder install-app-deps"}, "keywords": ["screen-recorder", "electron", "video-compression"], "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffmpeg-installer/win32-x64": "^4.1.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "ffmpeg-static": "^4.4.1", "fluent-ffmpeg": "^2.1.2"}, "build": {"appId": "com.screenrecorder.pro", "productName": "Screen Recorder Pro", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "!node_modules/electron/**/*", "!node_modules/electron-builder/**/*"], "extraResources": [{"from": "node_modules/@ffmpeg-installer/win32-x64/ffmpeg.exe", "to": "ffmpeg.exe"}], "win": {"target": [{"target": "dir", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "sign": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Screen Recorder Pro"}, "mac": {"target": "dmg", "category": "public.app-category.video"}, "linux": {"target": "AppImage", "category": "Video"}}}