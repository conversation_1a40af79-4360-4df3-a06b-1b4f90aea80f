const { app, BrowserWindow, ipcMain, desktopCapturer, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
// Importar fluent-ffmpeg apenas se disponível
let ffmpeg;
try {
  ffmpeg = require('fluent-ffmpeg');
} catch (error) {
  console.log('⚠️ fluent-ffmpeg não disponível, usando FFmpeg direto');
  ffmpeg = null;
}
const { checkPermissions } = require('./permissions');

// Configurar FFmpeg para usar o executável embutido
function findFFmpegPath() {
  const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
  // Verificar se app está disponível e se está empacotado
  let isPackaged = false;
  try {
    isPackaged = app && app.isPackaged;
  } catch (error) {
    // Se app não estiver disponível, assumir que não está empacotado
    isPackaged = false;
  }

  console.log(`🔧 Modo: ${isDev ? 'Desenvolvimento' : 'Produção'}, Empacotado: ${isPackaged}`);

  // Lista de caminhos possíveis para o FFmpeg
  const possiblePaths = [];

  if (isPackaged) {
    // Quando empacotado, o FFmpeg está em resources
    const resourcesPath = process.resourcesPath;
    possiblePaths.push(
      path.join(resourcesPath, 'ffmpeg.exe'),
      path.join(resourcesPath, 'ffmpeg-static.exe'),
      path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg.exe'),
      path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'ffmpeg-static', 'ffmpeg.exe')
    );
  } else {
    // Em desenvolvimento, usar os módulos node
    try {
      possiblePaths.push(require('@ffmpeg-installer/ffmpeg').path);
    } catch (e) { /* ignorar */ }

    try {
      possiblePaths.push(require('ffmpeg-static'));
    } catch (e) { /* ignorar */ }
  }

  // Adicionar caminhos de fallback
  possiblePaths.push(
    path.join(__dirname, '..', 'resources', 'ffmpeg.exe'),
    path.join(__dirname, '..', 'ffmpeg.exe'),
    path.join(process.cwd(), 'ffmpeg.exe'),
    'ffmpeg' // Tentar FFmpeg do sistema como último recurso
  );

  // Testar cada caminho
  for (const testPath of possiblePaths) {
    if (testPath && fs.existsSync(testPath)) {
      console.log(`✅ FFmpeg encontrado em: ${testPath}`);
      return testPath;
    }
  }

  console.error('❌ FFmpeg não encontrado em nenhum dos caminhos testados:');
  possiblePaths.forEach(p => console.log(`   - ${p}`));

  // Retornar o primeiro caminho como fallback
  return possiblePaths[0] || 'ffmpeg';
}

let ffmpegPath;

// Função para inicializar FFmpeg
function initializeFFmpeg() {
  ffmpegPath = findFFmpegPath();
  console.log('🔧 Configurando FFmpeg com caminho:', ffmpegPath);

  // Configurar FFmpeg apenas se disponível
  if (ffmpeg) {
    ffmpeg.setFfmpegPath(ffmpegPath);
  }
}

let mainWindow;
let areaSelectorWindow;
let isRecording = false;
let selectedArea = null;

// Testar FFmpeg na inicialização
function testFFmpeg() {
  return new Promise((resolve, reject) => {
    if (!ffmpeg) {
      // Se fluent-ffmpeg não está disponível, testar FFmpeg diretamente
      const { spawn } = require('child_process');
      const ffmpegProcess = spawn(ffmpegPath, ['-version'], { stdio: 'pipe' });

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ FFmpeg está funcionando corretamente!');
          resolve(true);
        } else {
          console.error('❌ FFmpeg não está disponível');
          reject(new Error('FFmpeg test failed'));
        }
      });

      ffmpegProcess.on('error', (err) => {
        console.error('❌ Erro ao testar FFmpeg:', err.message);
        reject(err);
      });

      return;
    }

    // Usar fluent-ffmpeg se disponível
    ffmpeg.getAvailableFormats((err, formats) => {
      if (err) {
        console.error('❌ FFmpeg não está disponível:', err.message);
        reject(err);
        return;
      }
      console.log('✅ FFmpeg está funcionando corretamente!');
      resolve(true);
    });
  });
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    titleBarStyle: 'hiddenInset',
    frame: false,
    backgroundColor: '#1a1a1a',
    show: false
  });

  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Capturar erros não tratados
  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('❌ Renderer process crashed:', { killed });
  });

  mainWindow.webContents.on('unresponsive', () => {
    console.error('❌ Renderer process became unresponsive');
  });

  // Atalho para DevTools
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.control && input.shift && input.key.toLowerCase() === 'i') {
      mainWindow.webContents.openDevTools();
    }
  });
}

app.whenReady().then(async () => {
  // Configurar flags para melhor captura de tela
  app.commandLine.appendSwitch('enable-usermedia-screen-capturing');
  app.commandLine.appendSwitch('allow-http-screen-capture');
  app.commandLine.appendSwitch('auto-select-desktop-capture-source', 'Screen Recorder Pro');

  try {
    // Inicializar FFmpeg agora que o app está pronto
    initializeFFmpeg();

    // Verificar permissões
    const permissions = await checkPermissions();
    console.log('🔐 Permissões verificadas:', permissions);

    await testFFmpeg();
    createWindow();
  } catch (error) {
    console.error('❌ Erro ao inicializar:', error.message);
    createWindow();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers
ipcMain.handle('get-sources', async () => {
  try {
    console.log('🔍 Buscando fontes de captura...');
    
    // Obter informações dos displays do sistema
    const { screen } = require('electron');
    const displays = screen.getAllDisplays();
    console.log(`🖥️  Sistema detectou ${displays.length} display(s):`, displays.map(d => `${d.bounds.width}x${d.bounds.height} @ ${d.bounds.x},${d.bounds.y}`));
    
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 300, height: 200 },
      fetchWindowIcons: true
    });
    
    console.log(`✅ Encontradas ${sources.length} fontes:`, sources.map(s => `${s.name} (${s.id})`));
    

    
    // Filtrar e organizar fontes
    const screens = [];
    const windows = [];
    const seenThumbnails = new Set(); // Para detectar thumbnails duplicadas
    const seenScreens = new Map(); // Para armazenar informações da tela
    
    // Função para gerar hash da thumbnail
    function getThumbnailHash(thumbnail) {
      try {
        const dataUrl = thumbnail.toDataURL();
        // Usar uma parte da string base64 como hash simples
        return dataUrl.substring(50, 150); // Pegar uma amostra do meio
      } catch (error) {
        return Math.random().toString(); // Fallback único
      }
    }
    
    // Função para verificar se duas thumbnails são similares
    function areThumbnailsSimilar(thumb1, thumb2) {
      try {
        // Primeiro verificar se as dimensões são diferentes
        const size1 = thumb1.getSize();
        const size2 = thumb2.getSize();
        
        // Se as dimensões são diferentes, provavelmente são monitores diferentes
        if (Math.abs(size1.width - size2.width) > 50 || Math.abs(size1.height - size2.height) > 50) {
          return false;
        }
        
        const hash1 = getThumbnailHash(thumb1);
        const hash2 = getThumbnailHash(thumb2);
        
        // Usar um threshold mais alto (95%) para ser mais conservador
        let similarity = 0;
        const minLength = Math.min(hash1.length, hash2.length);
        for (let i = 0; i < minLength; i++) {
          if (hash1[i] === hash2[i]) similarity++;
        }
        
        const similarityRatio = similarity / minLength;
        console.log(`🔍 Comparando thumbnails: ${(similarityRatio * 100).toFixed(1)}% similares`);
        
        // Só considerar duplicata se for mais de 95% similar
        return similarityRatio > 0.95;
      } catch (error) {
        console.error('Erro ao comparar thumbnails:', error);
        return false;
      }
    }
    
    sources.forEach(source => {
      if (!source.name || source.name.trim() === '' || !source.id) {
        return; // Pular fontes inválidas
      }
      
      if (source.id.includes('screen')) {
        let isDuplicate = false;
        
        // Verificar duplicatas por display_id primeiro
        if (source.display_id) {
          isDuplicate = screens.some(screen => screen.display_id === source.display_id);
          if (isDuplicate) {
            console.log(`🔄 Tela duplicada ignorada por display_id: ${source.name} (display_id: ${source.display_id})`);
          }
        }
        
        if (!isDuplicate) {
          // Melhorar nome da tela baseado na posição real
          let screenName = source.name;
          const screenCount = screens.length + 1;
          
          // Tentar mapear com displays do sistema
          let displayInfo = '';
          if (source.display_id && displays.length > 1) {
            const matchingDisplay = displays.find(d => d.id.toString() === source.display_id);
            if (matchingDisplay) {
              displayInfo = ` (${matchingDisplay.bounds.width}x${matchingDisplay.bounds.height})`;
            }
          }
          
          if (screenName === 'Entire Screen' || screenName.includes('Screen')) {
            if (displays.length === 1 || screenCount === 1) {
              screenName = `Monitor Principal${displayInfo}`;
            } else {
              screenName = `Monitor ${screenCount}${displayInfo}`;
            }
          }
          
          // Adicionar informações sobre resolução se disponível
          try {
            const size = source.thumbnail.getSize();
            const aspectRatio = (size.width / size.height).toFixed(2);
            console.log(`📐 ${screenName}: thumbnail ${size.width}x${size.height} (ratio: ${aspectRatio})`);
          } catch (error) {
            // Ignorar erro de tamanho
          }
          
          screens.push({
            id: source.id,
            name: screenName,
            thumbnail: source.thumbnail,
            display_id: source.display_id,
            appIcon: source.appIcon,
            type: 'screen'
          });
          
          console.log(`✅ Tela adicionada: ${screenName} (display_id: ${source.display_id || 'N/A'})`);
        }
      } else {
        // Para janelas, filtrar melhor para pegar apenas janelas reais de aplicativos
        const skipWindows = [
          'Screen Recorder Pro', // Nossa própria janela
          'Windows Shell Experience Host',
          'Program Manager',
          'Desktop Window Manager',
          'dwm.exe',
          'Taskbar', // Barra de tarefas
          'Start', // Menu iniciar
          'Search', // Busca do Windows
          'Notification', // Notificações
          'Windows Input Experience', // Teclado virtual
          'Microsoft Text Input Application',
          'index.html', // Páginas web genéricas
          'Sistema de gravação', // Nosso próprio título
          'Kiro' // IDE Kiro
        ];
        
        // Verificar se é uma janela real (tem título específico e não é do sistema)
        const isSystemWindow = skipWindows.some(skip => 
          source.name.toLowerCase().includes(skip.toLowerCase())
        );
        
        // Verificar se tem um tamanho de thumbnail válido
        let hasValidSize = true;
        try {
          if (source.thumbnail) {
            const size = source.thumbnail.getSize();
            hasValidSize = size.width > 100 && size.height > 100;
            
            // Se a thumbnail tem o mesmo tamanho da tela, provavelmente é uma captura de tela inteira
            const isFullScreen = size.width >= 1920 || size.height >= 1080;
            if (isFullScreen) {
              hasValidSize = false; // Ignorar capturas de tela inteira mascaradas como janelas
            }
          }
        } catch (error) {
          // Se não conseguir obter o tamanho, assumir que é válido
          hasValidSize = true;
        }
        
        // Verificar se o nome da janela parece ser de um aplicativo real
        const hasValidName = source.name.length > 3 && 
          !source.name.includes('—') && // Muitas capturas de tela têm esse caractere
          !source.name.includes('•') && // Ou este
          !/^(Tela|Screen)\s*\d*$/i.test(source.name); // Não é "Tela X" ou "Screen X"
        
        // Filtro normal - apenas janelas reais de aplicativos
        if (!isSystemWindow && hasValidSize && hasValidName) {
          windows.push({
            id: source.id,
            name: source.name,
            thumbnail: source.thumbnail,
            display_id: source.display_id,
            appIcon: source.appIcon,
            type: 'window'
          });
        }
      }
    });
    
    // Ordenar: telas primeiro, depois janelas por nome
    const validSources = [
      ...screens.sort((a, b) => a.name.localeCompare(b.name)),
      ...windows.sort((a, b) => a.name.localeCompare(b.name))
    ];
    
    console.log(`📊 Fontes filtradas: ${screens.length} telas, ${windows.length} janelas`);
    
    return validSources;
  } catch (error) {
    console.error('❌ Erro ao obter fontes:', error);
    return [];
  }
});

ipcMain.handle('save-video', async (event, buffer, filename) => {
  try {
    const { filePath } = await dialog.showSaveDialog(mainWindow, {
      defaultPath: filename,
      filters: [
        { name: 'Vídeos', extensions: ['mp4', 'webm', 'avi'] }
      ]
    });

    if (filePath) {
      fs.writeFileSync(filePath, buffer);
      return filePath;
    }
    return null;
  } catch (error) {
    console.error('Erro ao salvar vídeo:', error);
    return null;
  }
});

ipcMain.handle('compress-video', async (event, inputPath, outputPath, options) => {
  return new Promise((resolve, reject) => {
    const { quality, targetSize, format } = options;
    
    console.log(`🎬 Iniciando compressão: ${inputPath} -> ${outputPath}`);
    console.log(`📊 Configurações: qualidade=${quality}, tamanho=${targetSize}MB, formato=${format}`);
    
    // Primeiro, analisar o arquivo de entrada para verificar se tem áudio
    ffmpeg.ffprobe(inputPath, (err, metadata) => {
      if (err) {
        console.error('❌ Erro ao analisar arquivo de entrada:', err.message);
        reject(new Error(`Erro ao analisar arquivo: ${err.message}`));
        return;
      }
      
      const hasAudio = metadata.streams.some(stream => stream.codec_type === 'audio');
      console.log(`🎵 Arquivo tem áudio: ${hasAudio ? 'Sim' : 'Não'}`);
      
      let command = ffmpeg(inputPath);
      
      // Configurações de vídeo baseadas na qualidade
      switch (quality) {
        case 'high':
          command = command.videoBitrate('2000k');
          break;
        case 'medium':
          command = command.videoBitrate('1000k');
          break;
        case 'low':
          command = command.videoBitrate('500k');
          break;
      }
      
      // Configurar codec de vídeo baseado no formato
      switch (format) {
        case 'mp4':
          command = command.videoCodec('libx264');
          break;
        case 'webm':
          command = command.videoCodec('libvpx-vp9');
          break;
        case 'avi':
          command = command.videoCodec('libx264');
          break;
      }
      
      // Configurar áudio apenas se o arquivo tiver áudio
      if (hasAudio) {
        switch (quality) {
          case 'high':
            command = command.audioBitrate('128k');
            break;
          case 'medium':
            command = command.audioBitrate('96k');
            break;
          case 'low':
            command = command.audioBitrate('64k');
            break;
        }
        
        // Codec de áudio baseado no formato
        switch (format) {
          case 'mp4':
            command = command.audioCodec('aac');
            break;
          case 'webm':
            command = command.audioCodec('libvorbis');
            break;
          case 'avi':
            command = command.audioCodec('mp3');
            break;
        }
      } else {
        // Se não tem áudio, não processar áudio
        command = command.noAudio();
      }
      
      // Adicionar opções de otimização
      command = command
        .addOption('-preset', 'medium') // Preset balanceado
        .addOption('-crf', '23') // Qualidade constante
        .addOption('-movflags', '+faststart'); // Para MP4, otimizar para streaming

      // Se especificou tamanho alvo, calcular bitrate
      if (targetSize) {
        console.log(`🎯 Calculando bitrate para tamanho alvo: ${targetSize}MB`);
        
        const duration = metadata.format.duration;
        const targetBitrate = Math.floor((targetSize * 8 * 1024) / duration); // Converter MB para kb
        
        console.log(`⏱️  Duração: ${duration}s, Bitrate calculado: ${targetBitrate}k`);
        
        command = command.videoBitrate(`${targetBitrate}k`);
      }
      
      // Executar comando
      command
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('🚀 Comando FFmpeg:', commandLine);
        })
        .on('progress', (progress) => {
          console.log(`📈 Progresso: ${Math.round(progress.percent || 0)}%`);
          mainWindow.webContents.send('compression-progress', progress.percent || 0);
        })
        .on('end', () => {
          console.log('✅ Compressão concluída com sucesso!');
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('❌ Erro na compressão:', err.message);
          console.error('❌ Detalhes do erro:', err);
          
          // Tentar uma abordagem mais simples se falhar
          console.log('🔄 Tentando compressão simplificada...');
          
          ffmpeg(inputPath)
            .videoCodec('libx264')
            .videoBitrate('1000k')
            .noAudio() // Remover áudio para evitar problemas
            .addOption('-preset', 'fast')
            .addOption('-crf', '28')
            .output(outputPath.replace(/\.[^/.]+$/, '_simple.mp4'))
            .on('end', () => {
              console.log('✅ Compressão simplificada concluída!');
              resolve(outputPath.replace(/\.[^/.]+$/, '_simple.mp4'));
            })
            .on('error', (simpleErr) => {
              console.error('❌ Erro na compressão simplificada:', simpleErr.message);
              reject(new Error(`Erro na compressão: ${simpleErr.message}`));
            })
            .run();
        })
        .run();
    });
  });
});

ipcMain.handle('open-file', async (event, filePath) => {
  shell.openPath(filePath);
});

ipcMain.handle('minimize-window', () => {
  mainWindow.minimize();
});

ipcMain.handle('close-window', () => {
  mainWindow.close();
});

ipcMain.handle('test-ffmpeg', async () => {
  try {
    await testFFmpeg();
    return { success: true, message: 'FFmpeg está funcionando corretamente!' };
  } catch (error) {
    return { success: false, message: `Erro no FFmpeg: ${error.message}` };
  }
});

ipcMain.handle('check-permissions', async () => {
  try {
    const permissions = await checkPermissions();
    return { success: true, permissions };
  } catch (error) {
    return { success: false, message: error.message };
  }
});

ipcMain.handle('open-area-selector', async () => {
  try {
    // Fechar janela anterior se existir
    if (areaSelectorWindow) {
      areaSelectorWindow.close();
    }

    // Obter informações da tela principal
    const { screen } = require('electron');
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;

    areaSelectorWindow = new BrowserWindow({
      width: width,
      height: height,
      x: 0,
      y: 0,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      movable: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'area-selector-preload.js')
      }
    });

    areaSelectorWindow.loadFile(path.join(__dirname, 'area-selector.html'));
    
    // Minimizar janela principal temporariamente
    if (mainWindow) {
      mainWindow.minimize();
    }

    areaSelectorWindow.on('closed', () => {
      areaSelectorWindow = null;
      // Restaurar janela principal
      if (mainWindow) {
        mainWindow.restore();
        mainWindow.focus();
      }
    });

    return true;
  } catch (error) {
    console.error('Erro ao abrir seletor de área:', error);
    return false;
  }
});

ipcMain.handle('area-selected', async (event, area) => {
  selectedArea = area;
  console.log('Área selecionada:', area);
  
  // Fechar janela de seleção
  if (areaSelectorWindow) {
    areaSelectorWindow.close();
  }
  
  // Notificar janela principal
  if (mainWindow) {
    mainWindow.webContents.send('area-selection-complete', area);
  }
  
  return true;
});

ipcMain.handle('area-selection-cancelled', async () => {
  selectedArea = null;
  
  // Fechar janela de seleção
  if (areaSelectorWindow) {
    areaSelectorWindow.close();
  }
  
  // Notificar janela principal
  if (mainWindow) {
    mainWindow.webContents.send('area-selection-cancelled');
  }
  
  return true;
});

ipcMain.handle('get-selected-area', async () => {
  return selectedArea;
});

ipcMain.handle('simple-compress-video', async (event, inputPath, outputPath) => {
  return new Promise((resolve, reject) => {
    console.log(`🎬 Iniciando compressão simplificada: ${inputPath} -> ${outputPath}`);
    
    ffmpeg(inputPath)
      .videoCodec('libx264')
      .videoBitrate('1000k')
      .noAudio() // Remover áudio para máxima compatibilidade
      .addOption('-preset', 'fast')
      .addOption('-crf', '28')
      .addOption('-movflags', '+faststart')
      .output(outputPath)
      .on('start', (commandLine) => {
        console.log('🚀 Comando FFmpeg simplificado:', commandLine);
      })
      .on('progress', (progress) => {
        console.log(`📈 Progresso: ${Math.round(progress.percent || 0)}%`);
        mainWindow.webContents.send('compression-progress', progress.percent || 0);
      })
      .on('end', () => {
        console.log('✅ Compressão simplificada concluída!');
        resolve(outputPath);
      })
      .on('error', (err) => {
        console.error('❌ Erro na compressão simplificada:', err.message);
        reject(new Error(`Erro na compressão simplificada: ${err.message}`));
      })
      .run();
  });
});

ipcMain.handle('check-file-exists', async (event, filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error('Erro ao verificar arquivo:', error);
    return false;
  }
});