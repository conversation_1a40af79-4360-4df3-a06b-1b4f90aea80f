<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selecionar Área</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            cursor: crosshair;
            overflow: hidden;
            user-select: none;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1000;
        }

        .selection-box {
            position: absolute;
            border: 2px solid #667eea;
            background: rgba(102, 126, 234, 0.1);
            display: none;
            z-index: 1001;
        }

        .selection-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            font-weight: 500;
            z-index: 1002;
            pointer-events: none;
        }

        .instructions {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            text-align: center;
            z-index: 1003;
            backdrop-filter: blur(10px);
        }

        .instructions h3 {
            margin-bottom: 8px;
            color: #667eea;
        }

        .instructions p {
            margin-bottom: 4px;
        }

        .instructions .shortcut {
            color: #667eea;
            font-weight: 600;
        }

        .cancel-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(244, 67, 54, 0.9);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            z-index: 1003;
            transition: background 0.2s;
        }

        .cancel-btn:hover {
            background: rgba(244, 67, 54, 1);
        }
    </style>
</head>

<body>
    <div class="overlay" id="overlay"></div>
    <div class="selection-box" id="selectionBox"></div>
    <div class="selection-info" id="selectionInfo"></div>

    <div class="instructions">
        <h3>Selecionar Área para Gravação</h3>
        <p>Clique e arraste para selecionar a área</p>
        <p><span class="shortcut">Enter</span> para confirmar • <span class="shortcut">Esc</span> para cancelar</p>
    </div>

    <button class="cancel-btn" id="cancelBtn">
        ✕ Cancelar
    </button>

    <script>
        class AreaSelector {
            constructor() {
                this.isSelecting = false;
                this.startX = 0;
                this.startY = 0;
                this.currentX = 0;
                this.currentY = 0;
                this.selectedArea = null;

                this.overlay = document.getElementById('overlay');
                this.selectionBox = document.getElementById('selectionBox');
                this.selectionInfo = document.getElementById('selectionInfo');
                this.cancelBtn = document.getElementById('cancelBtn');

                this.setupEventListeners();
            }

            setupEventListeners() {
                // Mouse events
                this.overlay.addEventListener('mousedown', this.startSelection.bind(this));
                document.addEventListener('mousemove', this.updateSelection.bind(this));
                document.addEventListener('mouseup', this.endSelection.bind(this));

                // Keyboard events
                document.addEventListener('keydown', this.handleKeydown.bind(this));

                // Cancel button
                this.cancelBtn.addEventListener('click', this.cancel.bind(this));
            }

            startSelection(e) {
                this.isSelecting = true;
                this.startX = e.clientX;
                this.startY = e.clientY;
                this.currentX = e.clientX;
                this.currentY = e.clientY;

                this.selectionBox.style.display = 'block';
                this.updateSelectionBox();
            }

            updateSelection(e) {
                if (!this.isSelecting) return;

                this.currentX = e.clientX;
                this.currentY = e.clientY;

                this.updateSelectionBox();
                this.updateSelectionInfo();
            }

            endSelection(e) {
                if (!this.isSelecting) return;

                this.isSelecting = false;

                const width = Math.abs(this.currentX - this.startX);
                const height = Math.abs(this.currentY - this.startY);

                // Área mínima de 50x50 pixels
                if (width < 50 || height < 50) {
                    this.selectionBox.style.display = 'none';
                    this.selectionInfo.style.display = 'none';
                    return;
                }

                this.selectedArea = {
                    x: Math.min(this.startX, this.currentX),
                    y: Math.min(this.startY, this.currentY),
                    width: width,
                    height: height
                };

                // Destacar a área selecionada
                this.selectionBox.style.border = '3px solid #4caf50';
                this.selectionBox.style.background = 'rgba(76, 175, 80, 0.1)';
            }

            updateSelectionBox() {
                const x = Math.min(this.startX, this.currentX);
                const y = Math.min(this.startY, this.currentY);
                const width = Math.abs(this.currentX - this.startX);
                const height = Math.abs(this.currentY - this.startY);

                this.selectionBox.style.left = x + 'px';
                this.selectionBox.style.top = y + 'px';
                this.selectionBox.style.width = width + 'px';
                this.selectionBox.style.height = height + 'px';
            }

            updateSelectionInfo() {
                const width = Math.abs(this.currentX - this.startX);
                const height = Math.abs(this.currentY - this.startY);

                this.selectionInfo.textContent = `${width} × ${height}`;
                this.selectionInfo.style.display = 'block';
                this.selectionInfo.style.left = (this.currentX + 10) + 'px';
                this.selectionInfo.style.top = (this.currentY - 30) + 'px';
            }

            handleKeydown(e) {
                if (e.key === 'Escape') {
                    this.cancel();
                } else if (e.key === 'Enter' && this.selectedArea) {
                    this.confirm();
                }
            }

            confirm() {
                if (this.selectedArea) {
                    // Enviar área selecionada para a janela principal
                    window.electronAPI.sendSelectedArea(this.selectedArea);
                    window.close();
                }
            }

            cancel() {
                window.electronAPI.cancelAreaSelection();
                window.close();
            }
        }

        // Inicializar seletor de área
        new AreaSelector();
    </script>
</body>

</html>