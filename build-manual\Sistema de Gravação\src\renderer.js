class ScreenRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.selectedSource = null;
        this.isRecording = false;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.recordings = JSON.parse(localStorage.getItem('recordings') || '[]');
        
        this.initializeApp();
    }

    async initializeApp() {
        this.setupEventListeners();
        this.setupTabNavigation();
        await this.loadSources();
        this.renderHistory();
    }

    setupEventListeners() {
        // Controles da janela
        document.getElementById('minimizeBtn').addEventListener('click', () => {
            window.electronAPI.minimizeWindow();
        });

        document.getElementById('closeBtn').addEventListener('click', () => {
            window.electronAPI.closeWindow();
        });

        // Botões de gravação
        document.getElementById('recordBtn').addEventListener('click', () => {
            this.startRecording();
        });

        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stopRecording();
        });

        // Botão de teste FFmpeg
        document.getElementById('testFFmpegBtn').addEventListener('click', () => {
            this.testFFmpeg();
        });

        // Botão para verificar permissões
        document.getElementById('checkPermissionsBtn').addEventListener('click', () => {
            this.checkPermissions();
        });

        // Botão para atualizar fontes
        document.getElementById('refreshSourcesBtn').addEventListener('click', () => {
            this.loadSources();
        });

        // Botão para seleção de área
        document.getElementById('selectAreaBtn').addEventListener('click', () => {
            this.openAreaSelector();
        });

        // Botão para limpar área selecionada
        document.getElementById('clearAreaBtn').addEventListener('click', () => {
            this.clearSelectedArea();
        });

        // Botão para toggle do preview
        document.getElementById('togglePreviewBtn').addEventListener('click', () => {
            this.togglePreview();
        });

        // Listener para progresso de compressão
        window.electronAPI.onCompressionProgress((progress) => {
            this.updateCompressionProgress(progress);
        });

        // Listeners para seleção de área
        window.electronAPI.onAreaSelectionComplete((area) => {
            this.handleAreaSelected(area);
        });

        window.electronAPI.onAreaSelectionCancelled(() => {
            this.handleAreaSelectionCancelled();
        });
    }

    setupTabNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const tabContents = document.querySelectorAll('.tab-content');

        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const targetTab = item.dataset.tab;
                
                // Remover classe active de todos os itens
                navItems.forEach(nav => nav.classList.remove('active'));
                tabContents.forEach(tab => tab.classList.remove('active'));
                
                // Adicionar classe active ao item clicado
                item.classList.add('active');
                document.getElementById(`${targetTab}-tab`).classList.add('active');
            });
        });
    }

    async loadSources() {
        try {
            const sources = await window.electronAPI.getSources();
            this.renderSources(sources);
        } catch (error) {
            console.error('Erro ao carregar fontes:', error);
        }
    }

    renderSources(sources) {
        const sourcesGrid = document.getElementById('sourcesGrid');
        sourcesGrid.innerHTML = '';

        if (sources.length === 0) {
            sourcesGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-desktop"></i>
                    <p>Nenhuma fonte encontrada</p>
                    <small>Clique em "Atualizar Fontes" ou verifique as permissões do sistema</small>
                </div>
            `;
            return;
        }

        // Separar telas e janelas
        const screens = sources.filter(s => s.type === 'screen' || s.id.includes('screen'));
        const windows = sources.filter(s => s.type === 'window' || !s.id.includes('screen'));

        // Renderizar telas
        if (screens.length > 0) {
            const screenHeader = document.createElement('div');
            screenHeader.className = 'sources-section-header';
            screenHeader.innerHTML = `
                <h4><i class="fas fa-desktop"></i> Telas (${screens.length})</h4>
            `;
            sourcesGrid.appendChild(screenHeader);

            screens.forEach(source => {
                sourcesGrid.appendChild(this.createSourceElement(source));
            });
        }

        // Renderizar janelas
        if (windows.length > 0) {
            const windowHeader = document.createElement('div');
            windowHeader.className = 'sources-section-header';
            windowHeader.innerHTML = `
                <h4><i class="fas fa-window-maximize"></i> Janelas (${windows.length})</h4>
            `;
            sourcesGrid.appendChild(windowHeader);

            windows.forEach(source => {
                sourcesGrid.appendChild(this.createSourceElement(source));
            });
        } else if (screens.length > 0) {
            // Se não há janelas, mostrar dica
            const noWindowsInfo = document.createElement('div');
            noWindowsInfo.className = 'info-message';
            noWindowsInfo.innerHTML = `
                <div class="info-content">
                    <i class="fas fa-info-circle"></i>
                    <p>Nenhuma janela de aplicativo encontrada.</p>
                    <small>Abra alguns aplicativos ou marque "Mostrar todas as fontes" para ver mais opções.</small>
                </div>
            `;
            sourcesGrid.appendChild(noWindowsInfo);
        }
    }

    createSourceElement(source) {
        const sourceElement = document.createElement('div');
        sourceElement.className = 'source-item';
        sourceElement.dataset.sourceId = source.id;
        
        // Criar URL da thumbnail de forma mais segura
        let thumbnailUrl = '';
        try {
            thumbnailUrl = source.thumbnail.toDataURL();
        } catch (error) {
            console.warn('Erro ao converter thumbnail:', error);
            thumbnailUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY3IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFByZXZpZXc8L3RleHQ+Cjwvc3ZnPg==';
        }
        
        // Determinar tipo e ícone
        const isScreen = source.type === 'screen' || source.id.includes('screen');
        const typeIcon = isScreen ? '🖥️' : '🪟';
        const typeText = isScreen ? 'Tela' : 'Janela';
        
        // Adicionar classe CSS baseada no tipo
        sourceElement.classList.add(isScreen ? 'source-screen' : 'source-window');
        
        sourceElement.innerHTML = `
            <div class="source-type-badge">
                <span>${typeIcon} ${typeText}</span>
            </div>
            <img src="${thumbnailUrl}" alt="${source.name}" class="source-thumbnail" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY3IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFByZXZpZXc8L3RleHQ+Cjwvc3ZnPg=='">
            <div class="source-info">
                <h3 title="${source.name}">${source.name}</h3>
                <p class="source-details">${typeText}${source.display_id ? ` • Display ${source.display_id}` : ''}</p>
            </div>
        `;

        sourceElement.addEventListener('click', () => {
            this.selectSource(source, sourceElement);
        });

        return sourceElement;
    }

    selectSource(source, element) {
        // Remover seleção anterior
        document.querySelectorAll('.source-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Selecionar nova fonte
        element.classList.add('selected');
        this.selectedSource = source;
        
        // Mostrar botão de iniciar gravação
        document.getElementById('startRecording').style.display = 'block';
    }

    async startRecording() {
        if (!this.selectedSource) {
            alert('Selecione uma fonte para gravar');
            return;
        }

        try {
            console.log('🎬 Iniciando gravação da fonte:', this.selectedSource.name);
            
            let stream;
            
            if (this.selectedSource.type === 'area') {
                // Para área personalizada, capturar tela inteira primeiro
                stream = await this.captureCustomArea();
            } else {
                // Configuração normal para fontes de tela/janela
                const constraints = {
                    audio: false,
                    video: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: this.selectedSource.id,
                            minWidth: 1280,
                            maxWidth: 1920,
                            minHeight: 720,
                            maxHeight: 1080
                        }
                    }
                };

                console.log('📋 Constraints:', constraints);
                stream = await navigator.mediaDevices.getUserMedia(constraints);
            }
            
            // Verificar codecs suportados
            let mimeType = 'video/webm;codecs=vp9';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'video/webm;codecs=vp8';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm';
                }
            }
            
            console.log('🎥 Usando MIME type:', mimeType);
            
            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: mimeType,
                videoBitsPerSecond: 2500000 // 2.5 Mbps
            });

            this.recordedChunks = [];
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.handleRecordingStop();
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.recordingStartTime = Date.now();
            
            // Atualizar UI
            document.getElementById('startRecording').style.display = 'none';
            document.getElementById('recordingControls').style.display = 'flex';
            
            // Mostrar preview se habilitado
            const showPreview = document.getElementById('showPreview').checked;
            if (showPreview) {
                this.showPreview(stream);
            }
            
            // Iniciar timer
            this.startRecordingTimer();

        } catch (error) {
            console.error('❌ Erro ao iniciar gravação:', error);
            
            let errorMessage = 'Erro ao iniciar gravação.';
            
            if (error.name === 'NotAllowedError') {
                errorMessage = 'Permissão negada para captura de tela. Verifique as configurações de privacidade do sistema.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'Fonte de captura não encontrada. Tente selecionar outra fonte.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage = 'Captura de tela não suportada neste navegador/sistema.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Erro ao acessar a fonte de captura. A fonte pode estar em uso.';
            }
            
            alert(errorMessage + '\n\nDetalhes técnicos: ' + error.message);
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;
            
            // Parar timer
            if (this.recordingTimer) {
                clearInterval(this.recordingTimer);
                this.recordingTimer = null;
            }
            
            // Atualizar UI
            document.getElementById('recordingControls').style.display = 'none';
            document.getElementById('startRecording').style.display = 'block';
            
            // Esconder preview
            this.hidePreview();
        }
    }

    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            document.getElementById('recordingTime').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    async handleRecordingStop() {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const buffer = await blob.arrayBuffer();
        const uint8Array = new Uint8Array(buffer);
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `recording-${timestamp}.webm`;
        
        try {
            const savedPath = await window.electronAPI.saveVideo(uint8Array, filename);
            
            if (savedPath) {
                // Adicionar ao histórico
                const recording = {
                    id: Date.now(),
                    filename: filename,
                    path: savedPath,
                    date: new Date().toLocaleString('pt-BR'),
                    size: this.formatFileSize(uint8Array.length)
                };
                
                this.recordings.unshift(recording);
                localStorage.setItem('recordings', JSON.stringify(this.recordings));
                await this.renderHistory();
                
                // Verificar se deve comprimir automaticamente
                const autoCompress = document.getElementById('autoCompress').checked;
                if (autoCompress) {
                    try {
                        await this.compressVideo(savedPath, recording);
                    } catch (error) {
                        console.error('Erro na compressão automática:', error);
                        // Não mostrar erro aqui, apenas log - o vídeo original já foi salvo
                    }
                }
                
                alert('Gravação salva com sucesso!');
            }
        } catch (error) {
            console.error('Erro ao salvar gravação:', error);
            alert('Erro ao salvar gravação');
        }
    }

    async compressVideo(inputPath, recording) {
        const quality = document.getElementById('qualitySelect').value;
        const format = document.getElementById('formatSelect').value;
        const targetSize = document.getElementById('targetSize').value;
        const useSimple = document.getElementById('simpleCompression').checked;
        
        const outputPath = inputPath.replace(/\.[^/.]+$/, `-compressed.${format}`);
        
        try {
            // Mostrar modal de progresso
            document.getElementById('compressionModal').classList.add('show');
            
            let compressedPath;
            
            if (useSimple) {
                // Usar compressão simplificada
                const simplePath = inputPath.replace(/\.[^/.]+$/, '-compressed.mp4');
                compressedPath = await window.electronAPI.simpleCompressVideo(inputPath, simplePath);
            } else {
                // Usar compressão normal
                const options = {
                    quality,
                    format,
                    targetSize: targetSize ? parseInt(targetSize) : null
                };
                compressedPath = await window.electronAPI.compressVideo(inputPath, outputPath, options);
            }
            
            // Esconder modal
            document.getElementById('compressionModal').classList.remove('show');
            
            // Atualizar histórico com versão comprimida
            recording.compressedPath = compressedPath;
            localStorage.setItem('recordings', JSON.stringify(this.recordings));
            await this.renderHistory();
            
            alert('Vídeo comprimido com sucesso!');
            
        } catch (error) {
            console.error('Erro na compressão:', error);
            document.getElementById('compressionModal').classList.remove('show');
            
            // Mensagem de erro mais específica
            let errorMessage = 'Erro ao comprimir vídeo.';
            
            if (error.message.includes('ffmpeg exited')) {
                errorMessage = 'Erro no FFmpeg durante a compressão. Tente usar a "compressão simplificada" nas configurações.';
            } else if (error.message.includes('codec')) {
                errorMessage = 'Erro de codec. Tente marcar "compressão simplificada" nas configurações.';
            } else if (error.message.includes('permission')) {
                errorMessage = 'Erro de permissão. Verifique se o arquivo não está sendo usado por outro programa.';
            }
            
            alert(errorMessage + '\n\nO vídeo original está disponível no histórico.');
        }
    }

    updateCompressionProgress(progress) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        const percentage = Math.round(progress || 0);
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${percentage}%`;
    }

    async renderHistory() {
        const historyList = document.getElementById('historyList');
        
        // Verificar quais arquivos ainda existem
        await this.cleanupMissingFiles();
        
        if (this.recordings.length === 0) {
            historyList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-video-slash"></i>
                    <p>Nenhuma gravação ainda</p>
                </div>
            `;
            return;
        }
        
        historyList.innerHTML = this.recordings.map((recording, index) => `
            <div class="history-item" data-recording-id="${recording.id}">
                <div class="history-info">
                    <h4>${recording.filename}</h4>
                    <p>${recording.date} • ${recording.size}</p>
                </div>
                <div class="history-actions">
                    <button class="btn btn-primary btn-small open-btn" data-file-path="${recording.path}">
                        <i class="fas fa-play"></i>
                        Abrir
                    </button>
                    ${recording.compressedPath ? `
                        <button class="btn btn-primary btn-small open-compressed-btn" data-file-path="${recording.compressedPath}">
                            <i class="fas fa-compress"></i>
                            Comprimido
                        </button>
                    ` : `
                        <button class="btn btn-primary btn-small compress-btn" data-file-path="${recording.path}" data-recording-id="${recording.id}">
                            <i class="fas fa-compress"></i>
                            Comprimir
                        </button>
                    `}
                    <button class="btn btn-danger btn-small remove-btn" data-recording-id="${recording.id}">
                        <i class="fas fa-trash"></i>
                        Remover
                    </button>
                </div>
            </div>
        `).join('');
        
        // Adicionar botão para limpar histórico se houver gravações
        if (this.recordings.length > 0) {
            historyList.innerHTML += `
                <div class="history-clear-section">
                    <button class="btn btn-secondary clear-history-btn">
                        <i class="fas fa-trash-alt"></i>
                        Limpar Histórico
                    </button>
                </div>
            `;
        }
        
        // Adicionar event listeners aos botões
        this.attachHistoryEventListeners();
    }

    attachHistoryEventListeners() {
        const historyList = document.getElementById('historyList');
        
        // Event listeners para botões de abrir arquivo
        historyList.addEventListener('click', (e) => {
            if (e.target.closest('.open-btn')) {
                const button = e.target.closest('.open-btn');
                const filePath = button.getAttribute('data-file-path');
                this.openFile(filePath);
            }
        });
        
        // Event listeners para botões de abrir arquivo comprimido
        historyList.addEventListener('click', (e) => {
            if (e.target.closest('.open-compressed-btn')) {
                const button = e.target.closest('.open-compressed-btn');
                const filePath = button.getAttribute('data-file-path');
                this.openFile(filePath);
            }
        });
        
        // Event listeners para botões de comprimir
        historyList.addEventListener('click', (e) => {
            if (e.target.closest('.compress-btn')) {
                const button = e.target.closest('.compress-btn');
                const filePath = button.getAttribute('data-file-path');
                const recordingId = parseInt(button.getAttribute('data-recording-id'));
                this.compressExisting(filePath, recordingId);
            }
        });
        
        // Event listeners para botões de remover
        historyList.addEventListener('click', (e) => {
            if (e.target.closest('.remove-btn')) {
                const button = e.target.closest('.remove-btn');
                const recordingId = parseInt(button.getAttribute('data-recording-id'));
                this.removeFromHistory(recordingId);
            }
        });
        
        // Event listeners para botão de limpar histórico
        historyList.addEventListener('click', (e) => {
            if (e.target.closest('.clear-history-btn')) {
                this.clearHistory();
            }
        });
    }

    async cleanupMissingFiles() {
        const validRecordings = [];
        
        for (const recording of this.recordings) {
            try {
                // Verificar se o arquivo original existe
                const originalExists = await window.electronAPI.checkFileExists(recording.path);
                
                // Verificar se o arquivo comprimido existe (se houver)
                let compressedExists = true;
                if (recording.compressedPath) {
                    compressedExists = await window.electronAPI.checkFileExists(recording.compressedPath);
                }
                
                // Manter apenas se pelo menos um dos arquivos existir
                if (originalExists || compressedExists) {
                    // Se o arquivo original não existe mas o comprimido sim, atualizar o path
                    if (!originalExists && compressedExists) {
                        recording.path = recording.compressedPath;
                        recording.compressedPath = null;
                    }
                    validRecordings.push(recording);
                } else {
                    console.log(`Arquivo removido do histórico: ${recording.filename}`);
                }
            } catch (error) {
                console.error(`Erro ao verificar arquivo ${recording.filename}:`, error);
                // Em caso de erro, manter o registro
                validRecordings.push(recording);
            }
        }
        
        // Atualizar o array e localStorage apenas se houve mudanças
        if (validRecordings.length !== this.recordings.length) {
            this.recordings = validRecordings;
            localStorage.setItem('recordings', JSON.stringify(this.recordings));
        }
    }

    async removeFromHistory(recordingId) {
        if (confirm('Tem certeza que deseja remover esta gravação do histórico?')) {
            this.recordings = this.recordings.filter(r => r.id !== recordingId);
            localStorage.setItem('recordings', JSON.stringify(this.recordings));
            await this.renderHistory();
        }
    }

    async clearHistory() {
        if (confirm('Tem certeza que deseja limpar todo o histórico? Esta ação não pode ser desfeita.')) {
            this.recordings = [];
            localStorage.setItem('recordings', JSON.stringify(this.recordings));
            await this.renderHistory();
        }
    }

    async openFile(filePath) {
        try {
            await window.electronAPI.openFile(filePath);
        } catch (error) {
            console.error('Erro ao abrir arquivo:', error);
            alert('Erro ao abrir arquivo');
        }
    }

    async compressExisting(inputPath, recordingId) {
        const recording = this.recordings.find(r => r.id === recordingId);
        if (recording) {
            await this.compressVideo(inputPath, recording);
        }
    }

    async testFFmpeg() {
        const testBtn = document.getElementById('testFFmpegBtn');
        const resultDiv = document.getElementById('ffmpegTestResult');
        
        // Mostrar loading
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testando...';
        testBtn.disabled = true;
        resultDiv.style.display = 'none';
        
        try {
            const result = await window.electronAPI.testFFmpeg();
            
            resultDiv.textContent = result.message;
            resultDiv.className = `test-result ${result.success ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
            
        } catch (error) {
            resultDiv.textContent = `Erro ao testar FFmpeg: ${error.message}`;
            resultDiv.className = 'test-result error';
            resultDiv.style.display = 'block';
        } finally {
            testBtn.innerHTML = '<i class="fas fa-flask"></i> Testar FFmpeg';
            testBtn.disabled = false;
        }
    }

    async checkPermissions() {
        const testBtn = document.getElementById('checkPermissionsBtn');
        const resultDiv = document.getElementById('permissionsTestResult');
        
        // Mostrar loading
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando...';
        testBtn.disabled = true;
        resultDiv.style.display = 'none';
        
        try {
            const result = await window.electronAPI.checkPermissions();
            
            if (result.success) {
                const permissions = result.permissions;
                const message = `Captura de tela: ${permissions.screen ? '✅ Permitida' : '❌ Negada'}`;
                resultDiv.textContent = message;
                resultDiv.className = `test-result ${permissions.screen ? 'success' : 'error'}`;
            } else {
                resultDiv.textContent = result.message;
                resultDiv.className = 'test-result error';
            }
            
            resultDiv.style.display = 'block';
            
        } catch (error) {
            resultDiv.textContent = `Erro ao verificar permissões: ${error.message}`;
            resultDiv.className = 'test-result error';
            resultDiv.style.display = 'block';
        } finally {
            testBtn.innerHTML = '<i class="fas fa-shield-alt"></i> Verificar Permissões';
            testBtn.disabled = false;
        }
    }

    async openAreaSelector() {
        try {
            const success = await window.electronAPI.openAreaSelector();
            if (!success) {
                alert('Erro ao abrir seletor de área');
            }
        } catch (error) {
            console.error('Erro ao abrir seletor de área:', error);
            alert('Erro ao abrir seletor de área');
        }
    }

    handleAreaSelected(area) {
        console.log('Área selecionada:', area);
        
        // Criar um item de fonte personalizado para a área selecionada
        this.selectedSource = {
            id: 'custom-area',
            name: `Área Personalizada (${area.width}×${area.height})`,
            type: 'area',
            area: area
        };

        // Limpar seleções anteriores
        document.querySelectorAll('.source-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Mostrar status de área selecionada
        document.getElementById('areaSelectionStatus').style.display = 'block';
        
        // Adicionar item visual para área selecionada
        this.addCustomAreaItem(area);
        
        // Mostrar botão de iniciar gravação
        document.getElementById('startRecording').style.display = 'block';
    }

    clearSelectedArea() {
        // Limpar área selecionada
        this.selectedSource = null;
        
        // Esconder status
        document.getElementById('areaSelectionStatus').style.display = 'none';
        
        // Remover item de área personalizada
        const customAreaItem = document.querySelector('.custom-area-item');
        if (customAreaItem) {
            customAreaItem.remove();
        }
        
        // Esconder botão de gravação
        document.getElementById('startRecording').style.display = 'none';
        
        console.log('Área personalizada removida');
    }

    handleAreaSelectionCancelled() {
        console.log('Seleção de área cancelada');
    }

    async captureCustomArea() {
        // Para área personalizada, precisamos capturar a tela inteira primeiro
        // e depois recortar a área desejada usando canvas
        
        // Obter fonte da tela principal
        const sources = await window.electronAPI.getSources(false);
        const screenSource = sources.find(s => s.type === 'screen' || s.id.includes('screen'));
        
        if (!screenSource) {
            throw new Error('Não foi possível encontrar uma fonte de tela para captura de área');
        }
        
        // Capturar tela inteira
        const constraints = {
            audio: false,
            video: {
                mandatory: {
                    chromeMediaSource: 'desktop',
                    chromeMediaSourceId: screenSource.id
                }
            }
        };
        
        const fullScreenStream = await navigator.mediaDevices.getUserMedia(constraints);
        
        // Criar um stream processado com a área recortada
        return this.cropStreamToArea(fullScreenStream, this.selectedSource.area);
    }

    cropStreamToArea(stream, area) {
        // Criar elementos de vídeo e canvas para recortar
        const video = document.createElement('video');
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Configurar canvas com o tamanho da área selecionada
        canvas.width = area.width;
        canvas.height = area.height;
        
        video.srcObject = stream;
        video.play();
        video.muted = true; // Evitar feedback de áudio
        
        // Criar novo stream a partir do canvas
        const canvasStream = canvas.captureStream(30); // 30 FPS
        
        // Função para desenhar o frame recortado
        const drawFrame = () => {
            if (video.readyState >= 2 && !video.paused) { // HAVE_CURRENT_DATA
                try {
                    ctx.drawImage(
                        video,
                        area.x, area.y, area.width, area.height, // Área de origem
                        0, 0, area.width, area.height // Área de destino
                    );
                } catch (error) {
                    console.warn('Erro ao desenhar frame:', error);
                }
            }
            
            if (canvasStream.active) {
                requestAnimationFrame(drawFrame);
            }
        };
        
        video.addEventListener('loadeddata', () => {
            console.log('📹 Vídeo carregado para recorte, iniciando desenho');
            drawFrame();
        });
        
        // Armazenar referências para limpeza
        canvasStream._originalVideo = video;
        canvasStream._originalStream = stream;
        canvasStream._canvas = canvas;
        
        // Parar o stream original quando o novo stream for parado
        canvasStream.addEventListener('inactive', () => {
            console.log('📹 Stream de área personalizada parado');
            stream.getTracks().forEach(track => track.stop());
            video.srcObject = null;
            video.remove();
        });
        
        return canvasStream;
    }

    addCustomAreaItem(area) {
        const sourcesGrid = document.getElementById('sourcesGrid');
        
        // Remover item de área personalizada anterior se existir
        const existingCustomArea = sourcesGrid.querySelector('.custom-area-item');
        if (existingCustomArea) {
            existingCustomArea.remove();
        }

        // Criar novo item para área personalizada
        const customAreaElement = document.createElement('div');
        customAreaElement.className = 'source-item custom-area-item selected';
        customAreaElement.innerHTML = `
            <div class="source-type-badge">
                <span>✂️ Área</span>
            </div>
            <div class="custom-area-preview">
                <i class="fas fa-crop-alt"></i>
                <div class="area-info">
                    <h4>Área Personalizada</h4>
                    <p>${area.width} × ${area.height} pixels</p>
                    <small>Posição: ${area.x}, ${area.y}</small>
                </div>
            </div>
        `;

        // Inserir no início do grid
        sourcesGrid.insertBefore(customAreaElement, sourcesGrid.firstChild);
    }

    showPreview(stream) {
        const previewVideo = document.getElementById('previewVideo');
        const previewContainer = document.getElementById('recordingPreview');
        const previewSourceName = document.getElementById('previewSourceName');
        const previewResolution = document.getElementById('previewResolution');
        const previewTime = document.getElementById('previewTime');
        
        // Configurar vídeo de preview
        previewVideo.srcObject = stream;
        
        // Atualizar informações
        previewSourceName.textContent = this.selectedSource.name;
        
        // Obter resolução quando o vídeo carregar
        previewVideo.addEventListener('loadedmetadata', () => {
            const width = previewVideo.videoWidth;
            const height = previewVideo.videoHeight;
            previewResolution.textContent = `${width}x${height}`;
        });
        
        // Mostrar container
        previewContainer.style.display = 'block';
        
        // Atualizar timer do preview
        if (this.recordingTimer) {
            const updatePreviewTime = () => {
                const elapsed = Date.now() - this.recordingStartTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                previewTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            };
            updatePreviewTime();
            setInterval(updatePreviewTime, 1000);
        }
        
        console.log('📺 Preview iniciado');
    }

    hidePreview() {
        const previewVideo = document.getElementById('previewVideo');
        const previewContainer = document.getElementById('recordingPreview');
        
        // Parar stream do preview
        if (previewVideo.srcObject) {
            const tracks = previewVideo.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            previewVideo.srcObject = null;
        }
        
        // Esconder container
        previewContainer.style.display = 'none';
        
        console.log('📺 Preview parado');
    }

    togglePreview() {
        const previewContainer = document.getElementById('recordingPreview');
        const toggleBtn = document.getElementById('togglePreviewBtn');
        const previewVideo = document.getElementById('previewVideo');
        
        if (previewContainer.style.display === 'none' || !previewContainer.style.display) {
            // Mostrar preview
            previewContainer.style.display = 'block';
            toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Ocultar Preview';
            
            // Se estiver gravando, recriar o stream para o preview
            if (this.isRecording && this.mediaRecorder && this.mediaRecorder.stream) {
                previewVideo.srcObject = this.mediaRecorder.stream;
            }
        } else {
            // Esconder preview
            previewContainer.style.display = 'none';
            toggleBtn.innerHTML = '<i class="fas fa-eye"></i> Mostrar Preview';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Inicializar aplicação
const recorder = new ScreenRecorder();