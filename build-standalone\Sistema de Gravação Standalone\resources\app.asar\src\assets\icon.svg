<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="circle" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ee5a24;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="256" height="256" rx="32" fill="url(#bg)"/>
  
  <!-- Monitor/Desktop icon -->
  <rect x="48" y="80" width="160" height="120" rx="8" fill="rgba(255,255,255,0.9)"/>
  <rect x="56" y="88" width="144" height="104" rx="4" fill="#2c3e50"/>
  
  <!-- Recording circle -->
  <circle cx="128" cy="140" r="24" fill="url(#circle)"/>
  <circle cx="128" cy="140" r="18" fill="rgba(255,255,255,0.9)"/>
  <circle cx="128" cy="140" r="12" fill="#ff6b6b"/>
  
  <!-- Monitor stand -->
  <rect x="120" y="200" width="16" height="24" fill="rgba(255,255,255,0.9)"/>
  <rect x="108" y="224" width="40" height="8" rx="4" fill="rgba(255,255,255,0.9)"/>
</svg>
