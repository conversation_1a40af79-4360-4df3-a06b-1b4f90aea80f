
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> src</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.89% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>568/677</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">87.93% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>102/116</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">84.84% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>28/33</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.89% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>568/677</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="capabilities.ts"><a href="capabilities.ts.html">capabilities.ts</a></td>
	<td data-value="75.34" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.34" class="pct medium">75.34%</td>
	<td data-value="73" class="abs medium">55/73</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="7" class="abs high">6/7</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="7" class="abs medium">4/7</td>
	<td data-value="75.34" class="pct medium">75.34%</td>
	<td data-value="73" class="abs medium">55/73</td>
	</tr>

<tr>
	<td class="file medium" data-value="command.ts"><a href="command.ts.html">command.ts</a></td>
	<td data-value="69.81" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.81" class="pct medium">69.81%</td>
	<td data-value="106" class="abs medium">74/106</td>
	<td data-value="72.72" class="pct medium">72.72%</td>
	<td data-value="22" class="abs medium">16/22</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="5" class="abs high">4/5</td>
	<td data-value="69.81" class="pct medium">69.81%</td>
	<td data-value="106" class="abs medium">74/106</td>
	</tr>

<tr>
	<td class="file high" data-value="input.ts"><a href="input.ts.html">input.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="92" class="abs high">92/92</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="21" class="abs high">21/21</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="92" class="abs high">92/92</td>
	</tr>

<tr>
	<td class="file low" data-value="main.ts"><a href="main.ts.html">main.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	</tr>

<tr>
	<td class="file high" data-value="output.ts"><a href="output.ts.html">output.ts</a></td>
	<td data-value="97.97" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.97" class="pct high">97.97%</td>
	<td data-value="198" class="abs high">194/198</td>
	<td data-value="93.75" class="pct high">93.75%</td>
	<td data-value="32" class="abs high">30/32</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="97.97" class="pct high">97.97%</td>
	<td data-value="198" class="abs high">194/198</td>
	</tr>

<tr>
	<td class="file high" data-value="process.ts"><a href="process.ts.html">process.ts</a></td>
	<td data-value="86.93" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.93" class="pct high">86.93%</td>
	<td data-value="176" class="abs high">153/176</td>
	<td data-value="87.87" class="pct high">87.87%</td>
	<td data-value="33" class="abs high">29/33</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="86.93" class="pct high">86.93%</td>
	<td data-value="176" class="abs high">153/176</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2023-11-17T09:02:42.005Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    