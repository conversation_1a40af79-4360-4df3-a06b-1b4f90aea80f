{"name": "sistema-de-gravacao", "version": "1.0.0", "description": "Sistema de gravação de tela com Electron", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "build:portable": "electron-builder --win portable", "build:installer": "electron-builder --win nsis", "build:all": "npm run build:installer && npm run build:portable", "pack": "electron-builder --dir", "dist": "electron-builder", "test-ffmpeg": "node -e \"console.log('FFmpeg paths:'); try { console.log('@ffmpeg-installer:', require('@ffmpeg-installer/ffmpeg').path); } catch(e) { console.log('@ffmpeg-installer: not found'); } try { console.log('ffmpeg-static:', require('ffmpeg-static')); } catch(e) { console.log('ffmpeg-static: not found'); }\"", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "screen-recorder", "video-recording", "ffmpeg"], "author": "<PERSON>", "license": "MIT", "dependencies": {"fluent-ffmpeg": "^2.1.2", "@ffmpeg-installer/ffmpeg": "^1.1.0", "ffmpeg-static": "^5.2.0"}, "build": {"appId": "com.sistemadegravacao.app", "productName": "Sistema de Gravação", "directories": {"output": "build"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "node_modules/@ffmpeg-installer/win32-x64/ffmpeg.exe", "to": "ffmpeg.exe"}, {"from": "node_modules/ffmpeg-static/ffmpeg.exe", "to": "ffmpeg-static.exe"}], "win": {"target": "dir", "sign": false}}}