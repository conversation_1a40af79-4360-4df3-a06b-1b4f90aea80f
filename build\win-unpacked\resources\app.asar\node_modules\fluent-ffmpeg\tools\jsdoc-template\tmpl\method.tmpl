<?js
var data = obj;
var self = this;
?>
<dt>
    <h4 class="name" id="<?js= id ?>"><?js= data.attribs + (kind === 'class' ? 'new ' : '') + name + (kind !== 'event' ? data.signature : '') ?></h4>

    <?js if (data.summary) { ?>
    <p class="summary"><?js= summary ?></p>
    <?js } ?>
</dt>
<dd>

    <?js if (data.description) { ?>
    <div class="description">
        <?js= data.description ?>
    </div>
    <?js } ?>

    <?js if (kind === 'event' && data.type && data.type.names) {?>
        <h5>Type:</h5>
        <ul>
            <li>
                <?js= self.partial('type.tmpl', data.type.names) ?>
            </li>
        </ul>
    <?js } ?>

    <?js if (data['this']) { ?>
        <h5>This:</h5>
        <ul><li><?js= this.linkto(data['this'], data['this']) ?></li></ul>
    <?js } ?>

    <?js if (!data.isAlias && data.params && params.length) { ?>
        <h5>Parameters:</h5>
        <?js= this.partial('params.tmpl', params) ?>
    <?js } ?>

    <?js= this.partial('details.tmpl', data) ?>

    <?js if (data.requires && data.requires.length) { ?>
    <h5>Requires:</h5>
    <ul><?js data.requires.forEach(function(r) { ?>
        <li><?js= self.linkto(r) ?></li>
    <?js }); ?></ul>
    <?js } ?>

    <?js if (data.fires && fires.length) { ?>
    <h5>Fires:</h5>
    <ul><?js fires.forEach(function(f) { ?>
        <li><?js= self.linkto(f) ?></li>
    <?js }); ?></ul>
    <?js } ?>

    <?js if (data.listens && listens.length) { ?>
    <h5>Listens to Events:</h5>
    <ul><?js listens.forEach(function(f) { ?>
        <li><?js= self.linkto(f) ?></li>
    <?js }); ?></ul>
    <?js } ?>

    <?js if (data.listeners && listeners.length) { ?>
    <h5>Listeners of This Event:</h5>
    <ul><?js listeners.forEach(function(f) { ?>
        <li><?js= self.linkto(f) ?></li>
    <?js }); ?></ul>
    <?js } ?>

    <?js if (data.exceptions && exceptions.length) { ?>
    <h5>Throws:</h5>
    <?js if (exceptions.length > 1) { ?><ul><?js
        exceptions.forEach(function(r) { ?>
            <li><?js= self.partial('exceptions.tmpl', r) ?></li>
        <?js });
    ?></ul><?js } else {
        exceptions.forEach(function(r) { ?>
            <?js= self.partial('exceptions.tmpl', r) ?>
        <?js });
    } } ?>

    <?js if (data.returns && returns.length) { ?>
    <h5>Returns:</h5>
    <?js if (returns.length > 1) { ?><ul><?js
        returns.forEach(function(r) { ?>
            <li><?js= self.partial('returns.tmpl', r) ?></li>
        <?js });
    ?></ul><?js } else {
        returns.forEach(function(r) { ?>
            <?js= self.partial('returns.tmpl', r) ?>
        <?js });
    } } ?>

    <?js if (data.examples && examples.length) { ?>
        <h5>Example<?js= examples.length > 1? 's':'' ?>:</h5>
        <?js= this.partial('examples.tmpl', examples) ?>
    <?js } ?>

    <?js if (data.aliases && aliases.length) { ?>
        <h5>Alias<?js= aliases.length > 1 ? 'es' : '' ?>:</h5>
        <?js= this.partial('aliases.tmpl', { memberof: data.memberof, aliases: aliases }) ?>
    <?js } ?>
</dd>
