<?js
    var self = this;
    docs.forEach(function(doc, i) {
?>

<?js if (doc.kind === 'mainpage' || (doc.kind === 'package')) { ?>
    <?js= self.partial('mainpage.tmpl', doc) ?>
<?js } else if (doc.kind === 'source') { ?>
    <?js= self.partial('source.tmpl', doc) ?>
<?js } else { ?>

<section>

<header>
    <h2><?js if (doc.ancestors && doc.ancestors.length) { ?>
        <span class="ancestors"><?js= doc.ancestors.join('') ?></span>
    <?js } ?>
    <?js= doc.name ?>
    <?js if (doc.variation) { ?>
        <sup class="variation"><?js= doc.variation ?></sup>
    <?js } ?></h2>
    <?js if (doc.classdesc) { ?>
        <div class="class-description"><?js= doc.classdesc ?></div>
    <?js } ?>
</header>

<article>
    <div class="container-overview">
    <?js if (doc.kind === 'module' && doc.module) { ?>
        <?js= self.partial('method.tmpl', doc.module) ?>
    <?js } ?>

    <?js if (doc.kind === 'class') { ?>
        <?js= self.partial('method.tmpl', doc) ?>
    <?js } else { ?>
        <?js if (doc.description) { ?>
            <div class="description"><?js= doc.description ?></div>
        <?js } ?>

        <?js= self.partial('details.tmpl', doc) ?>

        <?js if (doc.examples && doc.examples.length) { ?>
            <h3>Example<?js= doc.examples.length > 1? 's':'' ?></h3>
            <?js= self.partial('examples.tmpl', doc.examples) ?>
        <?js } ?>
    <?js } ?>
    </div>

    <?js if (doc.augments && doc.augments.length) { ?>
        <h3 class="subsection-title">Extends</h3>

        <ul><?js doc.augments.forEach(function(a) { ?>
            <li><?js= self.linkto(a, a) ?></li>
        <?js }); ?></ul>
    <?js } ?>

    <?js if (doc.mixes && doc.mixes.length) { ?>
        <h3 class="subsection-title">Mixes In</h3>

        <ul><?js doc.mixes.forEach(function(a) { ?>
            <li><?js= self.linkto(a, a) ?></li>
        <?js }); ?></ul>
    <?js } ?>

    <?js if (doc.requires && doc.requires.length) { ?>
        <h3 class="subsection-title">Requires</h3>

        <ul><?js doc.requires.forEach(function(r) { ?>
            <li><?js= self.linkto(r, r) ?></li>
        <?js }); ?></ul>
    <?js } ?>

    <?js
        var classes = self.find({kind: 'class', memberof: doc.longname});
        if (doc.kind !== 'globalobj' && classes && classes.length) {
    ?>
        <h3 class="subsection-title">Classes</h3>

        <dl><?js classes.forEach(function(c) { ?>
            <dt><?js= self.linkto(c.longname, c.name) ?></dt>
            <dd><?js if (c.summary) { ?><?js= c.summary ?><?js } ?></dd>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var namespaces = self.find({kind: 'namespace', memberof: doc.longname});
        if (doc.kind !== 'globalobj' && namespaces && namespaces.length) {
    ?>
        <h3 class="subsection-title">Namespaces</h3>

        <dl><?js namespaces.forEach(function(n) { ?>
            <dt><a href="namespaces.html#<?js= n.longname ?>"><?js= self.linkto(n.longname, n.name) ?></a></dt>
            <dd><?js if (n.summary) { ?><?js= n.summary ?><?js } ?></dd>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var members = self.find({kind: 'member', memberof: title === 'Global' ? {isUndefined: true} : doc.longname});
        if (members && members.length && members.forEach) {
    ?>
        <h3 class="subsection-title">Members</h3>

        <dl><?js members.forEach(function(p) { ?>
            <?js= self.partial('members.tmpl', p) ?>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var methods = self.find({kind: 'function', memberof: title === 'Global' ? {isUndefined: true} : doc.longname});
        if (methods && methods.length && methods.forEach) {
            // Categorize methods
            var categories = {};
            methods.forEach(function(method) {
                var category = (method.category || 'Other') + ' methods';
                if (!(category in categories)) {
                    categories[category] = [];
                }

                categories[category].push(method);
            });

            if (Object.keys(categories) === 1) {
                categories = { "Methods": categories['Other methods'] };
            }

            Object.keys(categories).sort().forEach(function(category) {
    ?>
        <h3 class="subsection-title"><a name="<?js= category.toLowerCase().replace(/[^a-z0-9]/gi, '-') ?>"></a><?js= category ?></h3>

        <dl><?js categories[category].forEach(function(m) { ?>
            <?js= self.partial('method.tmpl', m) ?>
        <?js }); ?></dl>
    <?js
            });
        }
    ?>

    <?js
        var typedefs = self.find({kind: 'typedef', memberof: title === 'Global' ? {isUndefined: true} : doc.longname});
        if (typedefs && typedefs.length && typedefs.forEach) {
    ?>
        <h3 class="subsection-title">Type Definitions</h3>

        <dl><?js typedefs.forEach(function(e) {
                if (e.signature) {
            ?>
                <?js= self.partial('method.tmpl', e) ?>
            <?js
                }
                else {
            ?>
                <?js= self.partial('members.tmpl', e) ?>
            <?js
                }
            }); ?></dl>
    <?js } ?>

    <?js
        var events = self.find({kind: 'event', memberof: title === 'Global' ? {isUndefined: true} : doc.longname});
        if (events && events.length && events.forEach) {
    ?>
        <h3 class="subsection-title">Events</h3>

        <dl><?js events.forEach(function(e) { ?>
            <?js= self.partial('method.tmpl', e) ?>
        <?js }); ?></dl>
    <?js } ?>
</article>

</section>
<?js } ?>

<?js }); ?>
