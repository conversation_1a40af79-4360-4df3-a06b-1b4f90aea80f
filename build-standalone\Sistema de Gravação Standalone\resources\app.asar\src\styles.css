* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Barra de título customizada */
.titlebar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-app-region: drag;
    position: relative;
}

.titlebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.titlebar-drag-region {
    flex: 1;
    display: flex;
    align-items: center;
    padding-left: 16px;
}

.titlebar-title {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #7877c6, #ff77c6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.titlebar-title i {
    background: linear-gradient(135deg, #7877c6, #ff77c6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 16px;
}

.titlebar-controls {
    display: flex;
    -webkit-app-region: no-drag;
}

.titlebar-button {
    width: 46px;
    height: 32px;
    border: none;
    background: transparent;
    color: #ffffff;
    cursor: pointer;
    transition: background-color 0.2s;
}

.titlebar-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.titlebar-button.close:hover {
    background: #ff5f57;
}

/* Container principal */
.container {
    display: flex;
    height: calc(100vh - 40px);
    position: relative;
}

/* Animações de entrada */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.sidebar {
    animation: fadeInLeft 0.6s ease-out;
}

.main-content {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.source-item {
    animation: fadeInUp 0.6s ease-out both;
}

.source-item:nth-child(1) { animation-delay: 0.1s; }
.source-item:nth-child(2) { animation-delay: 0.2s; }
.source-item:nth-child(3) { animation-delay: 0.3s; }
.source-item:nth-child(4) { animation-delay: 0.4s; }
.source-item:nth-child(5) { animation-delay: 0.5s; }
.source-item:nth-child(6) { animation-delay: 0.6s; }

/* Sidebar */
.sidebar {
    width: 280px;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(30px);
    border-right: 1px solid rgba(255, 255, 255, 0.05);
    padding: 32px 0;
    position: relative;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg, transparent, rgba(120, 119, 198, 0.3), transparent);
}

.logo {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 32px;
    margin-bottom: 48px;
    font-size: 22px;
    font-weight: 700;
    position: relative;
}

.logo i {
    font-size: 32px;
    background: linear-gradient(135deg, #7877c6, #ff77c6, #78dbff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 10px rgba(120, 119, 198, 0.3));
}

.logo span {
    background: linear-gradient(135deg, #ffffff, #e0e0ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0 16px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 24px;
    margin: 0 16px;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 15px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    transform: translateX(4px);
    box-shadow: 0 8px 32px rgba(120, 119, 198, 0.1);
}

.nav-item.active {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.2), rgba(255, 119, 198, 0.1));
    color: #ffffff;
    border: 1px solid rgba(120, 119, 198, 0.3);
    box-shadow: 
        0 8px 32px rgba(120, 119, 198, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateX(8px);
}

.nav-item.active i {
    color: #7877c6;
    filter: drop-shadow(0 0 8px rgba(120, 119, 198, 0.5));
}

.nav-item i {
    width: 16px;
    text-align: center;
}

/* Conteúdo principal */
.main-content {
    flex: 1;
    padding: 40px 48px;
    overflow-y: auto;
    position: relative;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Headers */
.capture-header,
.settings-header,
.history-header {
    margin-bottom: 32px;
}

.capture-header h2,
.settings-header h2,
.history-header h2 {
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff, #7877c6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.capture-header p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 18px;
    margin-bottom: 24px;
    font-weight: 400;
}

.capture-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.capture-controls {
    align-self: flex-end;
    margin-top: -40px;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
}

.toggle-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Grid de fontes */
.sources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.sources-section-header {
    grid-column: 1 / -1;
    margin: 32px 0 24px 0;
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.sources-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.sources-section-header h4 {
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 12px;
    letter-spacing: -0.01em;
}

.sources-section-header h4 i {
    color: #7877c6;
    filter: drop-shadow(0 0 8px rgba(120, 119, 198, 0.5));
}

.sources-section-header:first-child {
    margin-top: 0;
}

.source-item {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.source-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.05), rgba(255, 119, 198, 0.02));
    opacity: 0;
    transition: opacity 0.3s;
}

.source-item:hover::before {
    opacity: 1;
}

.source-item:hover {
    border-color: rgba(120, 119, 198, 0.4);
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(120, 119, 198, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.source-item.selected {
    border-color: #7877c6;
    background: rgba(120, 119, 198, 0.15);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 
        0 25px 50px rgba(120, 119, 198, 0.3),
        0 0 0 2px rgba(120, 119, 198, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.source-item.source-screen {
    border-left: 4px solid #4caf50;
}

.source-item.source-window {
    border-left: 4px solid #2196f3;
}

.source-type-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.source-item {
    position: relative;
}

.source-thumbnail {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-radius: 16px;
    margin-bottom: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s;
}

.source-item:hover .source-thumbnail {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
    transform: scale(1.02);
}

.source-info h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ffffff;
    letter-spacing: -0.01em;
}

.source-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.source-details {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Controles de gravação */
.recording-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(30px);
    border-radius: 24px;
    padding: 32px;
    margin-bottom: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 16px 64px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.recording-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 77, 77, 0.5), transparent);
}

.recording-info {
    display: flex;
    align-items: center;
    gap: 24px;
}

.recording-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.pulse {
    width: 16px;
    height: 16px;
    background: radial-gradient(circle, #ff4d4d, #ff1a1a);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
    box-shadow: 
        0 0 20px rgba(255, 77, 77, 0.6),
        0 0 40px rgba(255, 77, 77, 0.3);
}

@keyframes pulse {
    0% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 
            0 0 20px rgba(255, 77, 77, 0.6),
            0 0 40px rgba(255, 77, 77, 0.3);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.3);
        box-shadow: 
            0 0 30px rgba(255, 77, 77, 0.8),
            0 0 60px rgba(255, 77, 77, 0.5);
    }
    100% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 
            0 0 20px rgba(255, 77, 77, 0.6),
            0 0 40px rgba(255, 77, 77, 0.3);
    }
}

.recording-time {
    font-size: 32px;
    font-weight: 900;
    font-family: 'Inter', monospace;
    background: linear-gradient(135deg, #ffffff, #ff4d4d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(255, 77, 77, 0.3);
    letter-spacing: 0.05em;
}

/* Botões */
.btn {
    padding: 16px 32px;
    border: none;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.02em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #7877c6, #ff77c6);
    color: white;
    box-shadow: 0 8px 32px rgba(120, 119, 198, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 
        0 16px 48px rgba(120, 119, 198, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg, #8a89d4, #ff89d4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff5f57, #ff8a80);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(255, 95, 87, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 16px 48px rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-large {
    padding: 24px 48px;
    font-size: 18px;
    border-radius: 24px;
    box-shadow: 
        0 16px 64px rgba(120, 119, 198, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-large:hover {
    transform: translateY(-4px) scale(1.08);
    box-shadow: 
        0 24px 80px rgba(120, 119, 198, 0.5),
        0 0 0 2px rgba(255, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.start-recording {
    text-align: center;
}

/* Configurações */
.settings-grid {
    display: grid;
    gap: 24px;
    max-width: 600px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-group label {
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-group select,
.setting-group input[type="number"] {
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
}

.setting-group select:focus,
.setting-group input:focus {
    outline: none;
    border-color: #667eea;
}

.setting-group small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

.setting-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.test-result {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    display: none;
}

.test-result.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: #4caf50;
}

.test-result.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #f44336;
}

.button-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.info-message {
    grid-column: 1 / -1;
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.info-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
}

.info-content i {
    font-size: 24px;
    color: #2196f3;
}

.info-content p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

.info-content small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

/* Área personalizada */
.custom-area-item {
    border-left: 4px solid #ff9800 !important;
    background: rgba(255, 152, 0, 0.1) !important;
}

.custom-area-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 140px;
    text-align: center;
    gap: 16px;
}

.custom-area-preview i {
    font-size: 48px;
    color: #ff9800;
}

.area-info h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #ffffff;
}

.area-info p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
}

.area-info small {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

/* Status de seleção de área */
.area-selection-status {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-content i {
    color: #4caf50;
    font-size: 18px;
}

.status-content span {
    flex: 1;
    color: #4caf50;
    font-weight: 600;
}

/* Histórico */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.empty-state {
    text-align: center;
    padding: 64px 32px;
    color: rgba(255, 255, 255, 0.5);
    grid-column: 1 / -1; /* Ocupar toda a largura do grid */
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state small {
    display: block;
    margin-top: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.3);
}

.history-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-info h4 {
    font-size: 16px;
    margin-bottom: 4px;
}

.history-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.history-actions {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(40px);
    border-radius: 24px;
    padding: 48px;
    max-width: 480px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 32px 128px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.modal-header h3 {
    font-size: 20px;
    margin-bottom: 24px;
    text-align: center;
}

.progress-container {
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #7877c6, #ff77c6, #78dbff);
    width: 0%;
    transition: width 0.3s;
    position: relative;
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.5);
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 1.5s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    font-weight: 600;
}

.modal-body p {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

/* Preview da gravação */
.recording-preview {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    margin: 24px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.preview-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.preview-container {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    overflow: hidden;
}

#previewVideo {
    width: 100%;
    height: auto;
    max-height: 400px;
    display: block;
    border-radius: 12px;
}

.preview-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 16px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.preview-info span {
    background: rgba(0, 0, 0, 0.5);
    padding: 4px 8px;
    border-radius: 4px;
    backdrop-filter: blur(5px);
}

/* Scrollbar customizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Preview de gravação - animação */
.recording-preview {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.recording-preview.hidden {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

.preview-container {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: #ff5f57;
    display: flex;
    align-items: center;
    gap: 12px;
}

.preview-source-info {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.preview-content {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    aspect-ratio: 16/9;
    min-height: 300px;
}

#previewVideo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0.3) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    pointer-events: none;
}

.recording-indicator-large {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 95, 87, 0.9);
    backdrop-filter: blur(10px);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 16px;
    color: white;
    align-self: flex-start;
    box-shadow: 0 8px 25px rgba(255, 95, 87, 0.3);
}

.pulse-large {
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    animation: pulse-large 1.5s infinite;
}

@keyframes pulse-large {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.3); }
    100% { opacity: 1; transform: scale(1); }
}

.recording-stats {
    display: flex;
    gap: 24px;
    align-self: flex-end;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    padding: 16px 20px;
    border-radius: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    font-weight: 600;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: white;
    font-family: 'Courier New', monospace;
}

/* Animações de transição */
.sources-grid {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.sources-grid.fade-out {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    filter: blur(5px);
    pointer-events: none;
}

.area-selection-status {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.area-selection-status.fade-out {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    filter: blur(5px);
    pointer-events: none;
}

.start-recording {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.start-recording.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    pointer-events: none;
}

/* Estilos para os novos botões do histórico */
.history-clear-section {
    margin-top: 20px;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff3742, #ff2f3a);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
}

.btn-danger::before {
    background: linear-gradient(135deg, #ff2f3a, #ff1e2a);
}