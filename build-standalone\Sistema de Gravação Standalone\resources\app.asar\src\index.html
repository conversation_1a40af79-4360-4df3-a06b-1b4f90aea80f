<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; img-src 'self' data: blob:; media-src 'self' blob:;">
    <title>Screen Recorder Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>

<body>
    <!-- Barra de título customizada -->
    <div class="titlebar">
        <div class="titlebar-drag-region">
            <div class="titlebar-title">
                <i class="fas fa-video"></i>
                Screen Recorder Pro
            </div>
        </div>
        <div class="titlebar-controls">
            <button class="titlebar-button minimize" id="minimizeBtn">
                <i class="fas fa-minus"></i>
            </button>
            <button class="titlebar-button close" id="closeBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <i class="fas fa-video"></i>
                <span>Recorder Pro</span>
            </div>

            <nav class="nav-menu">
                <button class="nav-item active" data-tab="capture">
                    <i class="fas fa-desktop"></i>
                    <span>Capturar</span>
                </button>
                <button class="nav-item" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span>Configurações</span>
                </button>
                <button class="nav-item" data-tab="history">
                    <i class="fas fa-history"></i>
                    <span>Histórico</span>
                </button>
            </nav>
        </div>

        <!-- Conteúdo principal -->
        <div class="main-content">
            <!-- Tab: Captura -->
            <div class="tab-content active" id="capture-tab">
                <div class="capture-header">
                    <h2>Selecionar Fonte de Captura</h2>
                    <p>Escolha a tela ou janela que deseja gravar</p>
                    <div class="capture-controls">
                        <button class="btn btn-primary btn-small" id="refreshSourcesBtn">
                            <i class="fas fa-sync-alt"></i>
                            Atualizar Fontes
                        </button>
                        <button class="btn btn-secondary btn-small" id="selectAreaBtn">
                            <i class="fas fa-crop-alt"></i>
                            Selecionar Área
                        </button>
                    </div>
                </div>

                <div class="area-selection-status" id="areaSelectionStatus" style="display: none;">
                    <div class="status-content">
                        <i class="fas fa-check-circle"></i>
                        <span>Área personalizada selecionada</span>
                        <button class="btn btn-small btn-secondary" id="clearAreaBtn">
                            <i class="fas fa-times"></i>
                            Limpar
                        </button>
                    </div>
                </div>

                <div class="sources-grid" id="sourcesGrid">
                    <!-- Sources serão carregadas dinamicamente -->
                </div>

                <!-- Preview de gravação -->
                <div class="recording-preview" id="recordingPreview" style="display: none;">
                    <div class="preview-container">
                        <div class="preview-header">
                            <h3>Gravando</h3>
                            <div class="preview-source-info" id="previewSourceInfo">
                                <!-- Informações da fonte selecionada -->
                            </div>
                            <button class="btn btn-small btn-secondary" id="togglePreviewBtn">
                                <i class="fas fa-eye-slash"></i>
                                Ocultar Preview
                            </button>
                        </div>
                        <div class="preview-content">
                            <video id="previewVideo" autoplay muted></video>
                            <div class="preview-overlay">
                                <div class="recording-indicator-large">
                                    <div class="pulse-large"></div>
                                    <span>REC</span>
                                </div>
                                <div class="recording-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Tempo:</span>
                                        <span class="stat-value" id="previewTime">00:00</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Fonte:</span>
                                        <span class="stat-value" id="previewSourceName">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Resolução:</span>
                                        <span class="stat-value" id="previewResolution">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="recording-controls" id="recordingControls" style="display: none;">
                    <div class="recording-info">
                        <div class="recording-indicator">
                            <div class="pulse"></div>
                            <span>Gravando</span>
                        </div>
                        <div class="recording-time" id="recordingTime">00:00</div>
                    </div>

                    <div class="control-buttons">
                        <button class="btn btn-danger" id="stopBtn">
                            <i class="fas fa-stop"></i>
                            Parar Gravação
                        </button>
                    </div>
                </div>



                <div class="start-recording" id="startRecording" style="display: none;">
                    <button class="btn btn-primary btn-large" id="recordBtn">
                        <i class="fas fa-circle"></i>
                        Iniciar Gravação
                    </button>
                </div>
            </div>

            <!-- Tab: Configurações -->
            <div class="tab-content" id="settings-tab">
                <div class="settings-header">
                    <h2>Configurações de Gravação</h2>
                </div>

                <div class="settings-grid">
                    <div class="setting-group">
                        <label>Qualidade do Vídeo</label>
                        <select id="qualitySelect">
                            <option value="high">Alta (2000k bitrate)</option>
                            <option value="medium" selected>Média (1000k bitrate)</option>
                            <option value="low">Baixa (500k bitrate)</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>Formato de Saída</label>
                        <select id="formatSelect">
                            <option value="mp4" selected>MP4</option>
                            <option value="webm">WebM</option>
                            <option value="avi">AVI</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>Tamanho Alvo (MB)</label>
                        <input type="number" id="targetSize" placeholder="Ex: 50" min="1" max="1000">
                        <small>Deixe vazio para usar apenas a qualidade selecionada</small>
                    </div>

                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="autoCompress" checked>
                            Comprimir automaticamente após gravação
                        </label>
                    </div>

                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="simpleCompression">
                            Usar compressão simplificada (mais compatível)
                        </label>
                        <small>Recomendado se houver problemas de compressão</small>
                    </div>

                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="showPreview" checked>
                            Mostrar preview durante gravação
                        </label>
                        <small>Exibe uma prévia em tempo real do que está sendo gravado</small>
                    </div>

                    <div class="setting-group">
                        <label>Teste do Sistema</label>
                        <div class="button-group">
                            <button class="btn btn-primary" id="testFFmpegBtn">
                                <i class="fas fa-flask"></i>
                                Testar FFmpeg
                            </button>
                            <button class="btn btn-primary" id="checkPermissionsBtn">
                                <i class="fas fa-shield-alt"></i>
                                Verificar Permissões
                            </button>
                        </div>
                        <div id="ffmpegTestResult" class="test-result"></div>
                        <div id="permissionsTestResult" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- Tab: Histórico -->
            <div class="tab-content" id="history-tab">
                <div class="history-header">
                    <h2>Gravações Recentes</h2>
                </div>
                <div class="history-list" id="historyList">
                    <div class="empty-state">
                        <i class="fas fa-video-slash"></i>
                        <p>Nenhuma gravação ainda</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Compressão -->
    <div class="modal" id="compressionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Comprimindo Vídeo</h3>
            </div>
            <div class="modal-body">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">0%</div>
                </div>
                <p>Aguarde enquanto o vídeo está sendo comprimido...</p>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>

</html>